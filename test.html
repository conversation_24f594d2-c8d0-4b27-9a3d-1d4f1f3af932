<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Analyse-IT | AI Investigation Assistant</title>
    <link href="https://fonts.googleapis.com/css2?family=Orbitron:wght@400;700;900&family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Inter', sans-serif;
            background: linear-gradient(135deg, #0f1419 0%, #1a1f2e 50%, #0f1419 100%);
            color: #ffffff;
            min-height: 100vh;
            overflow-x: hidden;
        }

        /* Header */
        .header {
            background: rgba(25, 28, 36, 0.95);
            border-bottom: 2px solid #e74c3c;
            padding: 15px 30px;
            backdrop-filter: blur(10px);
            position: sticky;
            top: 0;
            z-index: 1000;
        }

        .header-content {
            display: flex;
            justify-content: space-between;
            align-items: center;
            max-width: 1400px;
            margin: 0 auto;
        }

        .logo {
            font-family: 'Orbitron', monospace;
            font-size: 1.8rem;
            font-weight: 900;
            color: #e74c3c;
            text-shadow: 0 0 10px rgba(231, 76, 60, 0.5);
        }

        .header-subtitle {
            font-size: 0.9rem;
            color: #3498db;
            margin-left: 20px;
            font-weight: 500;
        }

        /* Main Container */
        .main-container {
            margin: 30px 20px;
            padding: 0;
            display: flex;
            gap: 20px;
            height: calc(100vh - 120px);
        }

        /* Left Section - Main Content */
        .left-section {
            flex: 1;
            display: flex;
            flex-direction: column;
            gap: 20px;
        }

        /* Content Row */
        .content-row {
            display: flex;
            gap: 20px;
            min-height: 40vh;
            background: rgba(25, 28, 36, 0.8);
            border-radius: 12px;
            border: 1px solid #34495e;
            padding: 25px;
            backdrop-filter: blur(10px);
        }

        /* Column 1 - Media Placeholder (66%) */
        .media-column {
            flex: 2;
            display: flex;
            flex-direction: column;
            gap: 15px;
        }

        .media-placeholder {
            flex: 1;
            background: linear-gradient(45deg, #2c3e50, #34495e);
            border: 2px dashed #3498db;
            border-radius: 8px;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            min-height: 300px;
            position: relative;
            transition: all 0.3s ease;
        }

        .media-placeholder:hover {
            border-color: #e74c3c;
            background: linear-gradient(45deg, #2c3e50, #3d566e);
        }

        .media-placeholder-icon {
            font-size: 4rem;
            color: #3498db;
            margin-bottom: 15px;
        }

        .media-placeholder-text {
            font-size: 1.1rem;
            color: #bdc3c7;
            text-align: center;
            margin-bottom: 10px;
        }

        .media-placeholder-subtext {
            font-size: 0.9rem;
            color: #7f8c8d;
            text-align: center;
        }

        .file-input {
            position: absolute;
            width: 100%;
            height: 100%;
            opacity: 0;
            cursor: pointer;
        }

        .media-display {
            width: 100%;
            height: 100%;
            object-fit: contain;
            border-radius: 6px;
            display: none;
        }

        .media-display.active {
            display: block;
        }

        .media-info {
            position: absolute;
            bottom: 10px;
            left: 10px;
            background: rgba(0, 0, 0, 0.8);
            color: #ffffff;
            padding: 8px 12px;
            border-radius: 6px;
            font-size: 0.8rem;
            font-family: 'Courier New', monospace;
        }

        /* Column 2 - Setup Panel (33%) */
        .setup-column {
            flex: 1;
            background: rgba(52, 73, 94, 0.3);
            border-radius: 8px;
            padding: 25px;
            border: 1px solid #34495e;
        }

        .setup-title {
            font-family: 'Orbitron', monospace;
            font-size: 1.3rem;
            color: #e74c3c;
            margin-bottom: 25px;
            text-align: center;
            border-bottom: 2px solid #e74c3c;
            padding-bottom: 10px;
        }

        .form-group {
            margin-bottom: 20px;
        }

        .form-label {
            display: block;
            font-weight: 600;
            color: #ecf0f1;
            margin-bottom: 8px;
            font-size: 0.95rem;
        }

        .form-select, .form-checkbox-group {
            width: 100%;
            background: rgba(44, 62, 80, 0.8);
            border: 1px solid #34495e;
            border-radius: 6px;
            padding: 12px;
            color: #ecf0f1;
            font-size: 0.9rem;
            transition: all 0.3s ease;
        }

        .form-select:focus {
            outline: none;
            border-color: #3498db;
            box-shadow: 0 0 8px rgba(52, 152, 219, 0.3);
        }

        .checkbox-container {
            display: flex;
            flex-wrap: wrap;
            gap: 10px;
            margin-top: 8px;
        }

        .checkbox-item {
            display: flex;
            align-items: center;
            background: rgba(44, 62, 80, 0.6);
            padding: 8px 12px;
            border-radius: 20px;
            border: 1px solid #34495e;
            transition: all 0.3s ease;
            cursor: pointer;
            font-size: 0.85rem;
        }

        .checkbox-item:hover {
            background: rgba(52, 152, 219, 0.2);
            border-color: #3498db;
        }

        .checkbox-item input[type="checkbox"] {
            margin-right: 6px;
            accent-color: #e74c3c;
        }

        .checkbox-item input[type="checkbox"]:checked + span {
            color: #e74c3c;
            font-weight: 600;
        }

        /* Prompt Section */
        .prompt-section {
            background: rgba(25, 28, 36, 0.8);
            border-radius: 12px;
            border: 1px solid #34495e;
            padding: 25px;
            backdrop-filter: blur(10px);
        }

        .prompt-title {
            font-family: 'Orbitron', monospace;
            font-size: 1.2rem;
            color: #3498db;
            margin-bottom: 15px;
        }

        .prompt-display {
            background: rgba(44, 62, 80, 0.8);
            border: 1px solid #34495e;
            border-radius: 8px;
            padding: 20px;
            min-height: 120px;
            color: #ecf0f1;
            font-family: 'Courier New', monospace;
            font-size: 0.9rem;
            line-height: 1.5;
            white-space: pre-wrap;
            margin-bottom: 20px;
        }

        .process-button {
            background: linear-gradient(45deg, #e74c3c, #c0392b);
            border: none;
            border-radius: 8px;
            padding: 15px 40px;
            color: #ffffff;
            font-family: 'Orbitron', monospace;
            font-size: 1rem;
            font-weight: 700;
            cursor: pointer;
            transition: all 0.3s ease;
            box-shadow: 0 4px 15px rgba(231, 76, 60, 0.3);
            display: block;
            margin: 0 auto;
        }

        .process-button:hover {
            background: linear-gradient(45deg, #c0392b, #a93226);
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(231, 76, 60, 0.4);
        }

        .process-button:active {
            transform: translateY(0);
        }

        /* Right Column - Extracted Items */
        .extracted-column {
            width: 350px;
            background: rgba(25, 28, 36, 0.8);
            border-radius: 12px;
            border: 1px solid #34495e;
            padding: 25px;
            backdrop-filter: blur(10px);
            display: flex;
            flex-direction: column;
            gap: 20px;
        }

        .extracted-title {
            font-family: 'Orbitron', monospace;
            font-size: 1.2rem;
            color: #e74c3c;
            text-align: center;
            border-bottom: 2px solid #e74c3c;
            padding-bottom: 10px;
        }

        /* Progress Indicator */
        .progress-section {
            background: rgba(44, 62, 80, 0.6);
            border-radius: 8px;
            padding: 15px;
            border: 1px solid #34495e;
        }

        .progress-label {
            font-size: 0.9rem;
            color: #ecf0f1;
            margin-bottom: 10px;
            font-weight: 600;
        }

        .progress-bar {
            width: 100%;
            height: 20px;
            background: rgba(52, 73, 94, 0.8);
            border-radius: 10px;
            overflow: hidden;
            border: 1px solid #34495e;
        }

        .progress-fill {
            height: 100%;
            background: linear-gradient(90deg, #3498db, #2ecc71);
            width: 0%;
            transition: width 0.5s ease;
            border-radius: 10px;
            position: relative;
        }

        .progress-fill::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(90deg, transparent, rgba(255,255,255,0.3), transparent);
            animation: shimmer 2s infinite;
        }

        @keyframes shimmer {
            0% { transform: translateX(-100%); }
            100% { transform: translateX(100%); }
        }

        .progress-text {
            text-align: center;
            margin-top: 8px;
            font-size: 0.8rem;
            color: #bdc3c7;
            font-family: 'Courier New', monospace;
        }

        /* Extracted Items */
        .extracted-items {
            flex: 1;
            overflow-y: auto;
            max-height: 400px;
        }

        .extracted-item {
            background: rgba(52, 73, 94, 0.6);
            border-radius: 8px;
            padding: 12px;
            margin-bottom: 10px;
            border: 1px solid #34495e;
            transition: all 0.3s ease;
        }

        .extracted-item:hover {
            background: rgba(52, 73, 94, 0.8);
            border-color: #3498db;
        }

        .item-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 8px;
        }

        .item-type {
            font-weight: 600;
            color: #3498db;
            font-size: 0.9rem;
        }

        .item-timing {
            font-size: 0.8rem;
            color: #e74c3c;
            font-family: 'Courier New', monospace;
        }

        .item-description {
            font-size: 0.85rem;
            color: #ecf0f1;
            margin-bottom: 10px;
            line-height: 1.4;
        }

        .item-actions {
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .discard-button {
            background: linear-gradient(45deg, #e74c3c, #c0392b);
            border: none;
            border-radius: 4px;
            padding: 4px 8px;
            color: #ffffff;
            font-size: 0.7rem;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .discard-button:hover {
            background: linear-gradient(45deg, #c0392b, #a93226);
            transform: scale(1.05);
        }

        .confidence-score {
            font-size: 0.7rem;
            color: #2ecc71;
            font-weight: 600;
        }

        /* Responsive Design */
        @media (max-width: 1200px) {
            .extracted-column {
                width: 300px;
            }
        }

        @media (max-width: 768px) {
            .main-container {
                flex-direction: column;
                height: auto;
                margin: 20px 10px;
            }

            .content-row {
                flex-direction: column;
            }

            .extracted-column {
                width: 100%;
                order: -1;
            }

            .header {
                padding: 15px 20px;
            }

            .header-content {
                flex-direction: column;
                gap: 10px;
            }
        }
    </style>
</head>
<body>
    <!-- Header -->
    <div class="header">
        <div class="header-content">
            <div class="logo">ANALYSE-IT</div>
            <div class="header-subtitle">AI Investigation Assistant</div>
        </div>
    </div>

    <!-- Main Container -->
    <div class="main-container">
        <!-- Left Section - Main Content -->
        <div class="left-section">
            <!-- Row 1 - Main Content -->
            <div class="content-row">
                <!-- Column 1 - Media Placeholder (66%) -->
                <div class="media-column">
                    <div class="media-placeholder" id="mediaPlaceholder">
                        <input type="file" class="file-input" id="fileInput" accept="image/*,video/*">
                        <div class="media-placeholder-icon">📁</div>
                        <div class="media-placeholder-text">Click to load Image or Video</div>
                        <div class="media-placeholder-subtext">Supports: JPG, PNG, MP4, AVI, MOV</div>

                        <!-- Media Display Elements -->
                        <img class="media-display" id="imageDisplay" alt="Loaded Image">
                        <video class="media-display" id="videoDisplay" controls>
                            Your browser does not support the video tag.
                        </video>

                        <!-- Media Info -->
                        <div class="media-info" id="mediaInfo" style="display: none;"></div>
                    </div>
                </div>

                <!-- Column 2 - Setup Panel (33%) -->
                <div class="setup-column">
                    <div class="setup-title">🔧 INVESTIGATION SETUP</div>

                    <!-- Role Selection -->
                    <div class="form-group">
                        <label class="form-label">What is your Role:</label>
                        <select class="form-select" id="roleSelect">
                            <option value="">Select your role...</option>
                            <option value="chief-investigating-officer">Chief Investigating Officer</option>
                            <option value="investigating-officer">Investigating Officer</option>
                            <option value="detective">Detective</option>
                            <option value="forensic-analyst">Forensic Analyst</option>
                            <option value="security-analyst">Security Analyst</option>
                            <option value="intelligence-officer">Intelligence Officer</option>
                        </select>
                    </div>

                    <!-- Investigation Focus -->
                    <div class="form-group">
                        <label class="form-label">What are you looking for:</label>
                        <div class="checkbox-container">
                            <label class="checkbox-item">
                                <input type="checkbox" value="general">
                                <span>General</span>
                            </label>
                            <label class="checkbox-item">
                                <input type="checkbox" value="weapons">
                                <span>Weapons</span>
                            </label>
                            <label class="checkbox-item">
                                <input type="checkbox" value="vehicles">
                                <span>Vehicles</span>
                            </label>
                            <label class="checkbox-item">
                                <input type="checkbox" value="vehicle-behaviour">
                                <span>Vehicle Behaviour</span>
                            </label>
                            <label class="checkbox-item">
                                <input type="checkbox" value="anpr">
                                <span>ANPR</span>
                            </label>
                            <label class="checkbox-item">
                                <input type="checkbox" value="people">
                                <span>People</span>
                            </label>
                            <label class="checkbox-item">
                                <input type="checkbox" value="places">
                                <span>Places</span>
                            </label>
                        </div>
                    </div>

                    <!-- Video Transcription -->
                    <div class="form-group">
                        <label class="checkbox-item">
                            <input type="checkbox" id="transcribeVideo">
                            <span>Transcribe the Video</span>
                        </label>
                    </div>

                    <!-- Report Type -->
                    <div class="form-group">
                        <label class="form-label">Create a PDF Report:</label>
                        <select class="form-select" id="reportType">
                            <option value="">Select report type...</option>
                            <option value="summary">Summary</option>
                            <option value="description">Description</option>
                            <option value="full-report">Full Report</option>
                        </select>
                    </div>
                </div>
            </div>

            <!-- Prompt Section -->
            <div class="prompt-section">
                <div class="prompt-title">📝 Generated Investigation Prompt</div>
                <div class="prompt-display" id="promptDisplay">
                    Your investigation prompt will appear here based on your selections above...
                </div>
                <button class="process-button" id="processButton">
                    🔍 PROCESS INVESTIGATION
                </button>
            </div>
        </div>

        <!-- Right Column - Extracted Items (Full Height) -->
        <div class="extracted-column">
            <div class="extracted-title">🔍 EXTRACTED ITEMS & PROGRESS</div>

            <!-- Progress Section -->
            <div class="progress-section">
                <div class="progress-label">Analysis Progress</div>
                <div class="progress-bar">
                    <div class="progress-fill" id="progressFill"></div>
                </div>
                <div class="progress-text" id="progressText">Ready to process...</div>
            </div>

            <!-- Extracted Items -->
            <div class="extracted-items" id="extractedItems">
                <!-- Sample items will be populated here -->
                <div class="extracted-item">
                    <div class="item-header">
                        <div class="item-type">🚗 Vehicle</div>
                        <div class="item-timing">00:15</div>
                    </div>
                    <div class="item-description">White sedan, license plate ABC-123, moving northbound</div>
                    <div class="item-actions">
                        <button class="discard-button" onclick="discardItem(this)">Discard</button>
                        <div class="confidence-score">95% confidence</div>
                    </div>
                </div>

                <div class="extracted-item">
                    <div class="item-header">
                        <div class="item-type">👤 Person</div>
                        <div class="item-timing">00:32</div>
                    </div>
                    <div class="item-description">Individual in dark clothing, approximately 6ft tall</div>
                    <div class="item-actions">
                        <button class="discard-button" onclick="discardItem(this)">Discard</button>
                        <div class="confidence-score">87% confidence</div>
                    </div>
                </div>

                <div class="extracted-item">
                    <div class="item-header">
                        <div class="item-type">🔫 Weapon</div>
                        <div class="item-timing">01:05</div>
                    </div>
                    <div class="item-description">Possible handgun detected in subject's right hand</div>
                    <div class="item-actions">
                        <button class="discard-button" onclick="discardItem(this)">Discard</button>
                        <div class="confidence-score">78% confidence</div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // Global variables
        let currentFile = null;
        let currentFileType = null;

        document.addEventListener('DOMContentLoaded', function() {
            console.log('Analyse-IT Investigation Assistant loaded successfully');

            // File input handling
            const fileInput = document.getElementById('fileInput');
            const mediaPlaceholder = document.getElementById('mediaPlaceholder');
            const imageDisplay = document.getElementById('imageDisplay');
            const videoDisplay = document.getElementById('videoDisplay');
            const mediaInfo = document.getElementById('mediaInfo');

            fileInput.addEventListener('change', function(e) {
                if (e.target.files.length > 0) {
                    const file = e.target.files[0];
                    currentFile = file;
                    loadMediaFile(file);
                }
            });

            function loadMediaFile(file) {
                const fileType = file.type;
                const fileName = file.name;
                const fileSize = (file.size / (1024 * 1024)).toFixed(2); // MB

                // Hide placeholder content
                const placeholderIcon = mediaPlaceholder.querySelector('.media-placeholder-icon');
                const placeholderText = mediaPlaceholder.querySelector('.media-placeholder-text');
                const placeholderSubtext = mediaPlaceholder.querySelector('.media-placeholder-subtext');

                placeholderIcon.style.display = 'none';
                placeholderText.style.display = 'none';
                placeholderSubtext.style.display = 'none';

                // Hide all media displays first
                imageDisplay.classList.remove('active');
                videoDisplay.classList.remove('active');

                // Create file URL
                const fileURL = URL.createObjectURL(file);

                if (fileType.startsWith('image/')) {
                    // Display image
                    currentFileType = 'image';
                    imageDisplay.src = fileURL;
                    imageDisplay.classList.add('active');

                    // Show media info
                    mediaInfo.innerHTML = `📷 ${fileName}<br>Size: ${fileSize} MB`;
                    mediaInfo.style.display = 'block';

                    console.log('Image loaded:', fileName);

                } else if (fileType.startsWith('video/')) {
                    // Display video
                    currentFileType = 'video';
                    videoDisplay.src = fileURL;
                    videoDisplay.classList.add('active');

                    // Show media info
                    mediaInfo.innerHTML = `🎥 ${fileName}<br>Size: ${fileSize} MB`;
                    mediaInfo.style.display = 'block';

                    console.log('Video loaded:', fileName);

                    // Update transcribe checkbox visibility
                    const transcribeCheckbox = document.getElementById('transcribeVideo');
                    transcribeCheckbox.parentElement.style.display = 'flex';
                } else {
                    alert('Unsupported file type. Please select an image or video file.');
                    resetMediaDisplay();
                }

                // Generate prompt after loading media
                generatePrompt();
            }

            function resetMediaDisplay() {
                // Show placeholder content
                const placeholderIcon = mediaPlaceholder.querySelector('.media-placeholder-icon');
                const placeholderText = mediaPlaceholder.querySelector('.media-placeholder-text');
                const placeholderSubtext = mediaPlaceholder.querySelector('.media-placeholder-subtext');

                placeholderIcon.style.display = 'block';
                placeholderText.style.display = 'block';
                placeholderSubtext.style.display = 'block';

                // Hide media displays
                imageDisplay.classList.remove('active');
                videoDisplay.classList.remove('active');
                mediaInfo.style.display = 'none';

                currentFile = null;
                currentFileType = null;
            }

            // Form change handlers for prompt generation
            const formElements = document.querySelectorAll('select, input[type="checkbox"]');
            formElements.forEach(element => {
                element.addEventListener('change', generatePrompt);
            });

            function generatePrompt() {
                const role = document.getElementById('roleSelect').value;
                const reportType = document.getElementById('reportType').value;
                const transcribeVideo = document.getElementById('transcribeVideo').checked;

                // Get selected investigation focuses
                const focuses = [];
                document.querySelectorAll('input[type="checkbox"]:checked').forEach(checkbox => {
                    if (checkbox.id !== 'transcribeVideo') {
                        focuses.push(checkbox.value);
                    }
                });

                // Generate prompt
                let prompt = 'INVESTIGATION ANALYSIS REQUEST\n\n';

                if (role) {
                    prompt += `Role: ${role.replace('-', ' ').toUpperCase()}\n`;
                }

                if (currentFile) {
                    prompt += `Media: ${currentFile.name} (${currentFileType})\n`;
                }

                if (focuses.length > 0) {
                    prompt += `Focus Areas: ${focuses.join(', ').toUpperCase()}\n`;
                }

                if (transcribeVideo && currentFileType === 'video') {
                    prompt += `Transcription: REQUIRED\n`;
                }

                if (reportType) {
                    prompt += `Report Type: ${reportType.replace('-', ' ').toUpperCase()}\n`;
                }

                prompt += '\n--- ANALYSIS INSTRUCTIONS ---\n';
                prompt += 'Please analyze the provided media and generate a comprehensive investigation report based on the specified parameters.';

                document.getElementById('promptDisplay').textContent = prompt;
            }

            // Process button
            document.getElementById('processButton').addEventListener('click', function() {
                if (!currentFile) {
                    alert('Please load an image or video file first.');
                    return;
                }

                console.log('Processing investigation for:', currentFile.name);
                startProcessing();
            });
        });

        // Progress simulation
        function startProcessing() {
            const progressFill = document.getElementById('progressFill');
            const progressText = document.getElementById('progressText');

            let progress = 0;
            progressText.textContent = 'Initializing analysis...';

            const interval = setInterval(() => {
                progress += Math.random() * 15;
                if (progress > 100) progress = 100;

                progressFill.style.width = progress + '%';

                if (progress < 25) {
                    progressText.textContent = 'Loading media file...';
                } else if (progress < 50) {
                    progressText.textContent = 'Analyzing frames...';
                } else if (progress < 75) {
                    progressText.textContent = 'Detecting objects...';
                } else if (progress < 100) {
                    progressText.textContent = 'Generating report...';
                } else {
                    progressText.textContent = 'Analysis complete!';
                    clearInterval(interval);

                    // Add some new extracted items
                    setTimeout(() => {
                        addExtractedItem('🏢 Building', currentFileType === 'video' ? '02:15' : 'Region 4', 'Government building identified in background', '92%');
                    }, 500);

                    setTimeout(() => {
                        addExtractedItem('📱 Device', currentFileType === 'video' ? '02:45' : 'Region 7', 'Mobile phone visible in subject\'s hand', '85%');
                    }, 1000);
                }
            }, 200);
        }

        // Add extracted item
        function addExtractedItem(type, timing, description, confidence) {
            const extractedItems = document.getElementById('extractedItems');
            const itemDiv = document.createElement('div');
            itemDiv.className = 'extracted-item';
            itemDiv.style.opacity = '0';
            itemDiv.style.transform = 'translateY(20px)';

            itemDiv.innerHTML = `
                <div class="item-header">
                    <div class="item-type">${type}</div>
                    <div class="item-timing">${timing}</div>
                </div>
                <div class="item-description">${description}</div>
                <div class="item-actions">
                    <button class="discard-button" onclick="discardItem(this)">Discard</button>
                    <div class="confidence-score">${confidence} confidence</div>
                </div>
            `;

            extractedItems.appendChild(itemDiv);

            // Animate in
            setTimeout(() => {
                itemDiv.style.transition = 'all 0.3s ease';
                itemDiv.style.opacity = '1';
                itemDiv.style.transform = 'translateY(0)';
            }, 100);
        }

        // Discard item function
        function discardItem(button) {
            const item = button.closest('.extracted-item');
            item.style.transition = 'all 0.3s ease';
            item.style.opacity = '0';
            item.style.transform = 'translateX(100%)';

            setTimeout(() => {
                item.remove();
            }, 300);
        }
    </script>
</body>
</html>
