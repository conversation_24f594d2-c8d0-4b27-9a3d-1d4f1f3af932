<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Analyse-IT | AI Investigation Assistant</title>
    <link href="https://fonts.googleapis.com/css2?family=Orbitron:wght@400;700;900&family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <!-- PDF Generation Library -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/jspdf/2.5.1/jspdf.umd.min.js"></script>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Inter', sans-serif;
            background: #000000;
            color: #ffffff;
            min-height: 100vh;
            overflow-x: hidden;
        }

        /* Header */
        .header {
            background: rgba(25, 28, 36, 0.95);
            border-bottom: 2px solid #e74c3c;
            padding: 15px 30px;
            backdrop-filter: blur(10px);
            position: sticky;
            top: 0;
            z-index: 1000;
        }

        .header-content {
            display: flex;
            justify-content: space-between;
            align-items: center;
            max-width: 1400px;
            margin: 0 auto;
        }

        .logo {
            font-family: 'Orbitron', monospace;
            font-size: 1.8rem;
            font-weight: 900;
            color: #e74c3c;
            text-shadow: 0 0 10px rgba(231, 76, 60, 0.5);
        }

        .header-subtitle {
            font-size: 0.9rem;
            color: #3498db;
            margin-left: 20px;
            font-weight: 500;
        }

        /* Main Container */
        .main-container {
            margin: 30px 20px;
            padding: 0;
            height: calc(100vh - 120px);
        }

        /* Three Column Layout */
        .three-column-layout {
            display: flex;
            gap: 20px;
            height: 100%;
            width: 100%;
        }

        /* Column 1 - Media and Progress (much wider) */
        .column-one {
            flex: 2.5;
            display: flex;
            flex-direction: column;
            gap: 20px;
        }

        /* Media Section */
        .media-section {
            flex: 1;
            background: rgba(25, 28, 36, 0.8);
            border-radius: 12px;
            border: 1px solid #34495e;
            padding: 25px;
            backdrop-filter: blur(10px);
        }

        /* Progress Section in Column 1 */
        .progress-section-col1 {
            background: rgba(25, 28, 36, 0.8);
            border-radius: 12px;
            border: 1px solid #34495e;
            padding: 25px;
            backdrop-filter: blur(10px);
            min-height: 150px;
        }

        .prompt-display {
            background: rgba(44, 62, 80, 0.8);
            border: 1px solid #34495e;
            border-radius: 8px;
            padding: 20px;
            color: #ecf0f1;
            font-family: 'Courier New', monospace;
            font-size: 0.9rem;
            line-height: 1.5;
            white-space: pre-wrap;
            flex: 1;
            overflow-y: auto;
            max-height: 150px;
        }

        /* Media Placeholder */
        .media-placeholder {
            flex: 1;
            background: linear-gradient(45deg, #2c3e50, #34495e);
            border: 2px dashed #3498db;
            border-radius: 8px;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            min-height: 300px;
            position: relative;
            transition: all 0.3s ease;
        }

        .media-placeholder:hover {
            border-color: #e74c3c;
            background: linear-gradient(45deg, #2c3e50, #3d566e);
        }

        .media-placeholder-icon {
            font-size: 4rem;
            color: #3498db;
            margin-bottom: 15px;
        }

        .media-placeholder-text {
            font-size: 1.1rem;
            color: #bdc3c7;
            text-align: center;
            margin-bottom: 10px;
        }

        .media-placeholder-subtext {
            font-size: 0.9rem;
            color: #7f8c8d;
            text-align: center;
        }

        .file-input {
            position: absolute;
            width: 100%;
            height: 100%;
            opacity: 0;
            cursor: pointer;
        }

        .media-display {
            width: 100%;
            height: 100%;
            object-fit: contain;
            border-radius: 6px;
            display: none;
        }

        .media-display.active {
            display: block;
        }

        .media-info {
            position: absolute;
            bottom: 10px;
            left: 10px;
            background: rgba(0, 0, 0, 0.8);
            color: #ffffff;
            padding: 8px 12px;
            border-radius: 6px;
            font-size: 0.8rem;
            font-family: 'Courier New', monospace;
        }

        /* Load New Media Button */
        .load-new-button {
            position: absolute;
            top: 10px;
            right: 10px;
            background: linear-gradient(45deg, #3498db, #2980b9);
            border: none;
            border-radius: 6px;
            padding: 8px 12px;
            color: #ffffff;
            font-size: 0.8rem;
            cursor: pointer;
            transition: all 0.3s ease;
            display: none;
            font-family: 'Orbitron', monospace;
            font-weight: 600;
        }

        .load-new-button:hover {
            background: linear-gradient(45deg, #2980b9, #1f618d);
            transform: scale(1.05);
        }

        .load-new-button.visible {
            display: block;
        }

        /* Column 2 - Setup Panel - Full Height (15% wider) */
        .column-two {
            flex: 1.15;
            background: rgba(52, 73, 94, 0.3);
            border-radius: 12px;
            padding: 25px;
            border: 1px solid #34495e;
            height: calc(100vh - 120px);
            overflow-y: auto;
            backdrop-filter: blur(10px);
            display: flex;
            flex-direction: column;
            gap: 20px;
        }

        /* Prompt Section in Column 2 */
        .prompt-section-col2 {
            background: rgba(25, 28, 36, 0.8);
            border-radius: 8px;
            border: 1px solid #34495e;
            padding: 20px;
            backdrop-filter: blur(10px);
            display: flex;
            flex-direction: column;
            gap: 15px;
            flex: 1;
        }

        .prompt-display-col2 {
            background: rgba(44, 62, 80, 0.8);
            border: 1px solid #34495e;
            border-radius: 8px;
            padding: 15px;
            color: #ecf0f1;
            font-family: 'Courier New', monospace;
            font-size: 0.85rem;
            line-height: 1.4;
            white-space: pre-wrap;
            flex: 1;
            overflow-y: auto;
            min-height: 100px;
        }

        .setup-title {
            font-family: 'Orbitron', monospace;
            font-size: 1.3rem;
            color: #e74c3c;
            margin-bottom: 25px;
            text-align: center;
            border-bottom: 2px solid #e74c3c;
            padding-bottom: 10px;
        }

        .form-group {
            margin-bottom: 20px;
        }

        .form-label {
            display: block;
            font-weight: 600;
            color: #ecf0f1;
            margin-bottom: 8px;
            font-size: 0.95rem;
        }

        .form-select, .form-checkbox-group {
            width: 100%;
            background: rgba(44, 62, 80, 0.8);
            border: 1px solid #34495e;
            border-radius: 6px;
            padding: 12px;
            color: #ecf0f1;
            font-size: 0.9rem;
            transition: all 0.3s ease;
        }

        .form-select:focus {
            outline: none;
            border-color: #3498db;
            box-shadow: 0 0 8px rgba(52, 152, 219, 0.3);
        }

        .checkbox-container {
            display: flex;
            flex-wrap: wrap;
            gap: 10px;
            margin-top: 8px;
        }

        .checkbox-item {
            display: flex;
            align-items: center;
            background: rgba(44, 62, 80, 0.6);
            padding: 8px 12px;
            border-radius: 20px;
            border: 1px solid #34495e;
            transition: all 0.3s ease;
            cursor: pointer;
            font-size: 0.85rem;
        }

        .checkbox-item:hover {
            background: rgba(52, 152, 219, 0.2);
            border-color: #3498db;
        }

        .checkbox-item input[type="checkbox"] {
            margin-right: 6px;
            accent-color: #e74c3c;
        }

        .checkbox-item input[type="checkbox"]:checked + span {
            color: #e74c3c;
            font-weight: 600;
        }



        .prompt-title {
            font-family: 'Orbitron', monospace;
            font-size: 1.2rem;
            color: #3498db;
            margin-bottom: 15px;
        }



        .process-button {
            background: linear-gradient(45deg, #e74c3c, #c0392b);
            border: none;
            border-radius: 8px;
            padding: 15px 40px;
            color: #ffffff;
            font-family: 'Orbitron', monospace;
            font-size: 1rem;
            font-weight: 700;
            cursor: pointer;
            transition: all 0.3s ease;
            box-shadow: 0 4px 15px rgba(231, 76, 60, 0.3);
            display: block;
            margin: 0 auto;
        }

        .process-button:hover {
            background: linear-gradient(45deg, #c0392b, #a93226);
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(231, 76, 60, 0.4);
        }

        .process-button:active {
            transform: translateY(0);
        }

        /* Button Container */
        .button-container {
            display: flex;
            gap: 15px;
            justify-content: center;
            align-items: center;
            flex-wrap: wrap;
        }

        /* Enhance Prompt Button */
        .enhance-button {
            background: linear-gradient(45deg, #f39c12, #e67e22);
            border: none;
            border-radius: 8px;
            padding: 12px 25px;
            color: #ffffff;
            font-family: 'Orbitron', monospace;
            font-size: 0.9rem;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            box-shadow: 0 4px 15px rgba(243, 156, 18, 0.3);
        }

        .enhance-button:hover {
            background: linear-gradient(45deg, #e67e22, #d35400);
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(243, 156, 18, 0.4);
        }

        /* PDF Button */
        .pdf-button {
            background: linear-gradient(45deg, #27ae60, #229954);
            border: none;
            border-radius: 8px;
            padding: 12px 25px;
            color: #ffffff;
            font-family: 'Orbitron', monospace;
            font-size: 0.9rem;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            box-shadow: 0 4px 15px rgba(39, 174, 96, 0.3);
            display: none;
        }

        .pdf-button:hover {
            background: linear-gradient(45deg, #229954, #1e8449);
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(39, 174, 96, 0.4);
        }

        .pdf-button.visible {
            display: inline-block;
        }

        /* No Items Message */
        .no-items-message {
            text-align: center;
            padding: 40px 20px;
            color: #7f8c8d;
        }

        .no-items-icon {
            font-size: 3rem;
            margin-bottom: 15px;
            opacity: 0.6;
        }

        .no-items-text {
            font-size: 1.1rem;
            font-weight: 600;
            margin-bottom: 8px;
            color: #34495e;
        }

        .no-items-subtext {
            font-size: 0.9rem;
            opacity: 0.8;
        }

        /* API Buttons */
        .api-buttons {
            display: flex;
            gap: 8px;
            margin: 10px 0;
            flex-wrap: wrap;
        }

        .api-btn {
            background: linear-gradient(45deg, #3498db, #2980b9);
            border: none;
            border-radius: 4px;
            padding: 6px 12px;
            color: #ffffff;
            font-size: 0.75rem;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            font-family: 'Orbitron', monospace;
        }

        .api-btn:hover {
            background: linear-gradient(45deg, #2980b9, #1f618d);
            transform: scale(1.05);
        }

        .anpr-btn {
            background: linear-gradient(45deg, #e74c3c, #c0392b);
        }

        .anpr-btn:hover {
            background: linear-gradient(45deg, #c0392b, #a93226);
        }

        .navic-btn {
            background: linear-gradient(45deg, #f39c12, #e67e22);
        }

        .navic-btn:hover {
            background: linear-gradient(45deg, #e67e22, #d35400);
        }

        .description-btn {
            background: linear-gradient(45deg, #9b59b6, #8e44ad);
        }

        .description-btn:hover {
            background: linear-gradient(45deg, #8e44ad, #7d3c98);
        }

        .facial-btn {
            background: linear-gradient(45deg, #1abc9c, #16a085);
        }

        .facial-btn:hover {
            background: linear-gradient(45deg, #16a085, #138d75);
        }

        .hand-btn, .long-btn, .traditional-btn, .other-btn {
            background: linear-gradient(45deg, #e67e22, #d35400);
        }

        .hand-btn:hover, .long-btn:hover, .traditional-btn:hover, .other-btn:hover {
            background: linear-gradient(45deg, #d35400, #ba4a00);
        }

        /* Column 3 - Extracted Items (same width as Column 2) */
        .column-three {
            flex: 1;
            background: rgba(25, 28, 36, 0.8);
            border-radius: 12px;
            border: 1px solid #34495e;
            padding: 25px;
            backdrop-filter: blur(10px);
            display: flex;
            flex-direction: column;
            gap: 20px;
            height: calc(100vh - 120px);
        }

        .extracted-title {
            font-family: 'Orbitron', monospace;
            font-size: 1.2rem;
            color: #e74c3c;
            text-align: center;
            border-bottom: 2px solid #e74c3c;
            padding-bottom: 10px;
        }

        /* Progress Indicator */
        .progress-section {
            background: rgba(44, 62, 80, 0.6);
            border-radius: 8px;
            padding: 15px;
            border: 1px solid #34495e;
        }

        .progress-label {
            font-size: 0.9rem;
            color: #ecf0f1;
            margin-bottom: 10px;
            font-weight: 600;
        }

        .progress-bar {
            width: 100%;
            height: 20px;
            background: rgba(52, 73, 94, 0.8);
            border-radius: 10px;
            overflow: hidden;
            border: 1px solid #34495e;
        }

        .progress-fill {
            height: 100%;
            background: linear-gradient(90deg, #3498db, #2ecc71);
            width: 0%;
            transition: width 0.5s ease;
            border-radius: 10px;
            position: relative;
        }

        .progress-fill::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(90deg, transparent, rgba(255,255,255,0.3), transparent);
            animation: shimmer 2s infinite;
        }

        @keyframes shimmer {
            0% { transform: translateX(-100%); }
            100% { transform: translateX(100%); }
        }

        .progress-text {
            text-align: center;
            margin-top: 8px;
            font-size: 0.8rem;
            color: #bdc3c7;
            font-family: 'Courier New', monospace;
        }

        /* Extracted Items */
        .extracted-items {
            flex: 1;
            overflow-y: auto;
            min-height: 0;
        }

        .extracted-item {
            background: rgba(52, 73, 94, 0.6);
            border-radius: 8px;
            padding: 12px;
            margin-bottom: 10px;
            border: 1px solid #34495e;
            transition: all 0.3s ease;
            display: flex;
            gap: 12px;
        }

        .extracted-item:hover {
            background: rgba(52, 73, 94, 0.8);
            border-color: #3498db;
        }

        /* Item Image */
        .item-image {
            width: 60px;
            height: 60px;
            background: linear-gradient(45deg, #2c3e50, #34495e);
            border: 1px solid #3498db;
            border-radius: 6px;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: all 0.3s ease;
            flex-shrink: 0;
            overflow: hidden;
        }

        .item-image:hover {
            border-color: #e74c3c;
            transform: scale(1.05);
        }

        .item-image img {
            width: 100%;
            height: 100%;
            object-fit: cover;
            border-radius: 4px;
        }

        .item-image-placeholder {
            color: #3498db;
            font-size: 1.5rem;
        }

        /* Item Content */
        .item-content {
            flex: 1;
            display: flex;
            flex-direction: column;
            gap: 8px;
        }

        .item-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 8px;
        }

        .item-type {
            font-weight: 600;
            color: #3498db;
            font-size: 0.9rem;
        }

        .item-timing {
            font-size: 0.8rem;
            color: #e74c3c;
            font-family: 'Courier New', monospace;
        }

        .item-description {
            font-size: 0.85rem;
            color: #ecf0f1;
            margin-bottom: 10px;
            line-height: 1.4;
        }

        .item-actions {
            display: flex;
            flex-direction: column;
            gap: 8px;
        }

        .action-buttons {
            display: flex;
            flex-wrap: wrap;
            gap: 4px;
            justify-content: flex-start;
        }

        .action-buttons-row {
            display: flex;
            justify-content: space-between;
            align-items: center;
            width: 100%;
        }

        .primary-buttons {
            display: flex;
            gap: 4px;
        }

        .api-buttons {
            display: flex;
            flex-wrap: wrap;
            gap: 4px;
            margin-top: 6px;
        }

        .discard-button {
            background: linear-gradient(45deg, #e74c3c, #c0392b);
            border: none;
            border-radius: 4px;
            padding: 4px 8px;
            color: #ffffff;
            font-size: 0.7rem;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .discard-button:hover {
            background: linear-gradient(45deg, #c0392b, #a93226);
            transform: scale(1.05);
        }

        .api-button {
            background: linear-gradient(45deg, #3498db, #2980b9);
            border: none;
            border-radius: 4px;
            padding: 4px 8px;
            color: #ffffff;
            font-size: 0.7rem;
            cursor: pointer;
            transition: all 0.3s ease;
            position: relative;
        }

        .api-button:hover {
            background: linear-gradient(45deg, #2980b9, #1f618d);
            transform: scale(1.05);
        }

        .api-button.anpr {
            background: linear-gradient(45deg, #f39c12, #e67e22);
        }

        .api-button.anpr:hover {
            background: linear-gradient(45deg, #e67e22, #d35400);
        }

        .api-button.navic {
            background: linear-gradient(45deg, #9b59b6, #8e44ad);
        }

        .api-button.navic:hover {
            background: linear-gradient(45deg, #8e44ad, #7d3c98);
        }

        /* Person API Buttons */
        .api-button.description {
            background: linear-gradient(45deg, #2ecc71, #27ae60);
        }

        .api-button.description:hover {
            background: linear-gradient(45deg, #27ae60, #229954);
        }

        .api-button.facial {
            background: linear-gradient(45deg, #e67e22, #d35400);
        }

        .api-button.facial:hover {
            background: linear-gradient(45deg, #d35400, #ba4a00);
        }

        /* Weapon API Buttons */
        .api-button.hand {
            background: linear-gradient(45deg, #e74c3c, #c0392b);
        }

        .api-button.hand:hover {
            background: linear-gradient(45deg, #c0392b, #a93226);
        }

        .api-button.long {
            background: linear-gradient(45deg, #8e44ad, #7d3c98);
        }

        .api-button.long:hover {
            background: linear-gradient(45deg, #7d3c98, #6c3483);
        }

        .api-button.traditional {
            background: linear-gradient(45deg, #d68910, #b7950b);
        }

        .api-button.traditional:hover {
            background: linear-gradient(45deg, #b7950b, #9a7d0a);
        }

        .api-button.other {
            background: linear-gradient(45deg, #5d6d7e, #515a5a);
        }

        .api-button.other:hover {
            background: linear-gradient(45deg, #515a5a, #424949);
        }

        /* Places API Button */
        .api-button.location {
            background: linear-gradient(45deg, #17a2b8, #138496);
        }

        .api-button.location:hover {
            background: linear-gradient(45deg, #138496, #0f6674);
        }

        /* Image Popup Modal */
        .image-modal {
            display: none;
            position: fixed;
            z-index: 2000;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.9);
            backdrop-filter: blur(5px);
        }

        .image-modal.active {
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .modal-content {
            position: relative;
            max-width: 80%;
            max-height: 80%;
            background: rgba(25, 28, 36, 0.95);
            border-radius: 12px;
            border: 2px solid #3498db;
            padding: 20px;
            backdrop-filter: blur(10px);
        }

        .modal-image {
            width: 100%;
            height: auto;
            max-height: 70vh;
            object-fit: contain;
            border-radius: 8px;
        }

        .modal-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 15px;
            padding-bottom: 10px;
            border-bottom: 1px solid #34495e;
        }

        .modal-title {
            font-family: 'Orbitron', monospace;
            color: #3498db;
            font-size: 1.1rem;
        }

        .modal-close {
            background: linear-gradient(45deg, #e74c3c, #c0392b);
            border: none;
            border-radius: 6px;
            padding: 8px 12px;
            color: #ffffff;
            cursor: pointer;
            font-size: 0.9rem;
            transition: all 0.3s ease;
        }

        .modal-close:hover {
            background: linear-gradient(45deg, #c0392b, #a93226);
            transform: scale(1.05);
        }

        .modal-info {
            margin-top: 15px;
            padding-top: 15px;
            border-top: 1px solid #34495e;
            color: #bdc3c7;
            font-size: 0.9rem;
        }

        /* Tooltip styles */
        .api-button::before {
            content: attr(data-tooltip);
            position: absolute;
            bottom: 100%;
            left: 50%;
            transform: translateX(-50%);
            background: rgba(0, 0, 0, 0.9);
            color: #ffffff;
            padding: 6px 10px;
            border-radius: 4px;
            font-size: 0.6rem;
            white-space: nowrap;
            opacity: 0;
            visibility: hidden;
            transition: all 0.3s ease;
            z-index: 1000;
            pointer-events: none;
        }

        .api-button::after {
            content: '';
            position: absolute;
            bottom: 100%;
            left: 50%;
            transform: translateX(-50%);
            border: 4px solid transparent;
            border-top-color: rgba(0, 0, 0, 0.9);
            opacity: 0;
            visibility: hidden;
            transition: all 0.3s ease;
            z-index: 1000;
            pointer-events: none;
        }

        .api-button:hover::before,
        .api-button:hover::after {
            opacity: 1;
            visibility: visible;
            bottom: calc(100% + 5px);
        }

        .confidence-score {
            font-size: 0.7rem;
            color: #2ecc71;
            font-weight: 600;
        }

        /* Responsive Design */
        @media (max-width: 1200px) {
            .extracted-column {
                width: 300px;
            }
        }

        @media (max-width: 768px) {
            .main-container {
                flex-direction: column;
                height: auto;
                margin: 20px 10px;
            }

            .content-row {
                flex-direction: column;
            }

            .extracted-column {
                width: 100%;
                order: -1;
            }

            .header {
                padding: 15px 20px;
            }

            .header-content {
                flex-direction: column;
                gap: 10px;
            }
        }
    </style>
</head>
<body>
    <!-- Header -->
    <div class="header">
        <div class="header-content">
            <div class="logo">ANALYSE-IT</div>
            <div class="header-subtitle">AI Investigation Assistant</div>
        </div>
    </div>

    <!-- Main Container -->
    <div class="main-container">
        <!-- Three Column Layout -->
        <div class="three-column-layout">

            <!-- Column 1 - Media and Progress -->
            <div class="column-one">
                <!-- Media Section -->
                <div class="media-section">
                    <div class="media-placeholder" id="mediaPlaceholder">
                        <input type="file" class="file-input" id="fileInput" accept="image/*,video/*">
                        <input type="file" class="file-input" id="newFileInput" accept="image/*,video/*" style="display: none;">
                        <div class="media-placeholder-icon">📁</div>
                        <div class="media-placeholder-text">Click to load Image or Video</div>
                        <div class="media-placeholder-subtext">Supports: JPG, PNG, MP4, AVI, MOV</div>

                        <!-- Media Display Elements -->
                        <img class="media-display" id="imageDisplay" alt="Loaded Image">
                        <video class="media-display" id="videoDisplay" controls>
                            Your browser does not support the video tag.
                        </video>

                        <!-- Media Info -->
                        <div class="media-info" id="mediaInfo" style="display: none;"></div>

                        <!-- Load New Media Button -->
                        <button class="load-new-button" id="loadNewButton" onclick="loadNewMedia()">
                            🔄 Load New Media
                        </button>
                    </div>
                </div>

                <!-- Progress Section -->
                <div class="progress-section-col1">
                    <div class="progress-label">Analysis Progress</div>
                    <div class="progress-bar">
                        <div class="progress-fill" id="progressFill"></div>
                    </div>
                    <div class="progress-text" id="progressText">Ready to process...</div>
                </div>
            </div>

            <!-- Column 2 - Investigation Setup -->
            <div class="column-two">
                <!-- Setup Section -->
                <div>
                    <div class="setup-title">🔧 INVESTIGATION SETUP</div>

                    <!-- Role Selection -->
                    <div class="form-group">
                        <label class="form-label">What is your Role:</label>
                        <select class="form-select" id="roleSelect">
                            <option value="">Select your role...</option>
                            <option value="chief-investigating-officer">Chief Investigating Officer</option>
                            <option value="investigating-officer">Investigating Officer</option>
                            <option value="detective">Detective</option>
                            <option value="forensic-analyst">Forensic Analyst</option>
                            <option value="security-analyst">Security Analyst</option>
                            <option value="intelligence-officer">Intelligence Officer</option>
                        </select>
                    </div>

                    <!-- Investigation Focus -->
                    <div class="form-group">
                        <label class="form-label">What are you looking for:</label>
                        <div class="checkbox-container">
                            <label class="checkbox-item">
                                <input type="checkbox" value="general">
                                <span>General</span>
                            </label>
                            <label class="checkbox-item">
                                <input type="checkbox" value="weapons">
                                <span>Weapons</span>
                            </label>
                            <label class="checkbox-item">
                                <input type="checkbox" value="vehicles">
                                <span>Vehicles</span>
                            </label>
                            <label class="checkbox-item">
                                <input type="checkbox" value="vehicle-behaviour">
                                <span>Vehicle Behaviour</span>
                            </label>
                            <label class="checkbox-item">
                                <input type="checkbox" value="anpr">
                                <span>ANPR</span>
                            </label>
                            <label class="checkbox-item">
                                <input type="checkbox" value="people">
                                <span>People</span>
                            </label>
                            <label class="checkbox-item">
                                <input type="checkbox" value="places">
                                <span>Places</span>
                            </label>
                        </div>
                    </div>

                    <!-- Video Transcription -->
                    <div class="form-group">
                        <label class="checkbox-item">
                            <input type="checkbox" id="transcribeVideo">
                            <span>Transcribe the Video</span>
                        </label>
                    </div>

                    <!-- Report Type -->
                    <div class="form-group">
                        <label class="form-label">Create a PDF Report:</label>
                        <select class="form-select" id="reportType">
                            <option value="">Select report type...</option>
                            <option value="summary">Summary</option>
                            <option value="description">Description</option>
                            <option value="full-report">Full Report</option>
                        </select>
                    </div>
                </div>

                <!-- Prompt Section -->
                <div class="prompt-section-col2">
                    <div class="prompt-title">📝 Generated Investigation Prompt</div>
                    <div class="prompt-display-col2" id="promptDisplay">
                        Your investigation prompt will appear here based on your selections...
                    </div>
                    <div class="button-container">
                        <button class="enhance-button" id="enhanceButton" onclick="enhancePrompt()">
                            ✨ Enhance Prompt
                        </button>
                        <button class="process-button" id="processButton">
                            🔍 PROCESS INVESTIGATION
                        </button>
                        <button class="pdf-button" id="pdfButton" onclick="generatePDFReport()">
                            📄 Generate PDF
                        </button>
                    </div>
                </div>
            </div>

            <!-- Column 3 - Extracted Items -->
            <div class="column-three">
                <div class="extracted-title">🔍 EXTRACTED ITEMS</div>

                <!-- Extracted Items -->
                <div class="extracted-items" id="extractedItems">
                    <!-- Extracted items will be populated here after analysis -->
                    <div class="no-items-message" id="noItemsMessage">
                        <div class="no-items-icon">🔍</div>
                        <div class="no-items-text">No items extracted yet</div>
                        <div class="no-items-subtext">Run analysis to detect and extract evidence</div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Image Modal -->
    <div class="image-modal" id="imageModal">
        <div class="modal-content">
            <div class="modal-header">
                <div class="modal-title" id="modalTitle">Extracted Item Image</div>
                <button class="modal-close" onclick="closeImageModal()">✕ Close</button>
            </div>
            <img class="modal-image" id="modalImage" src="" alt="Extracted Item">
            <div class="modal-info" id="modalInfo">
                Click on any extracted item image to view a larger version with enhanced details.
            </div>
        </div>
    </div>

    <script>
        // Global variables
        let currentFile = null;
        let currentFileType = null;
        let fileUri = "";
        let currentMimeType = "";

        // Gemini API Configuration
        const API_KEY = "AIzaSyDai77cOp3pRBFswBVsnQpGya4gM6C-E4k"; // Your API key
        const UPLOAD_URL = `https://generativelanguage.googleapis.com/upload/v1beta/files?key=${API_KEY}`;
        const ANALYZE_URL = `https://generativelanguage.googleapis.com/v1beta/models/gemini-1.5-flash-latest:generateContent?key=${API_KEY}`;

        document.addEventListener('DOMContentLoaded', function() {
            console.log('Analyse-IT Investigation Assistant loaded successfully');

            // File input handling
            const fileInput = document.getElementById('fileInput');
            const mediaPlaceholder = document.getElementById('mediaPlaceholder');
            const imageDisplay = document.getElementById('imageDisplay');
            const videoDisplay = document.getElementById('videoDisplay');
            const mediaInfo = document.getElementById('mediaInfo');

            fileInput.addEventListener('change', function(e) {
                if (e.target.files.length > 0) {
                    const file = e.target.files[0];
                    currentFile = file;
                    loadMediaFile(file);
                }
            });

            // New file input handler
            const newFileInput = document.getElementById('newFileInput');
            newFileInput.addEventListener('change', function(e) {
                if (e.target.files.length > 0) {
                    const file = e.target.files[0];
                    currentFile = file;
                    loadMediaFile(file);
                }
            });

            function loadMediaFile(file) {
                const fileType = file.type;
                const fileName = file.name;
                const fileSize = (file.size / (1024 * 1024)).toFixed(2); // MB

                // Hide placeholder content
                const placeholderIcon = mediaPlaceholder.querySelector('.media-placeholder-icon');
                const placeholderText = mediaPlaceholder.querySelector('.media-placeholder-text');
                const placeholderSubtext = mediaPlaceholder.querySelector('.media-placeholder-subtext');

                placeholderIcon.style.display = 'none';
                placeholderText.style.display = 'none';
                placeholderSubtext.style.display = 'none';

                // Hide all media displays first
                imageDisplay.classList.remove('active');
                videoDisplay.classList.remove('active');

                // Create file URL
                const fileURL = URL.createObjectURL(file);

                if (fileType.startsWith('image/')) {
                    // Display image
                    currentFileType = 'image';
                    imageDisplay.src = fileURL;
                    imageDisplay.classList.add('active');

                    // Show media info
                    mediaInfo.innerHTML = `📷 ${fileName}<br>Size: ${fileSize} MB`;
                    mediaInfo.style.display = 'block';

                    console.log('Image loaded:', fileName);

                } else if (fileType.startsWith('video/')) {
                    // Display video
                    currentFileType = 'video';
                    videoDisplay.src = fileURL;
                    videoDisplay.classList.add('active');

                    // Show media info
                    mediaInfo.innerHTML = `🎥 ${fileName}<br>Size: ${fileSize} MB`;
                    mediaInfo.style.display = 'block';

                    console.log('Video loaded:', fileName);

                    // Update transcribe checkbox visibility
                    const transcribeCheckbox = document.getElementById('transcribeVideo');
                    transcribeCheckbox.parentElement.style.display = 'flex';
                } else {
                    alert('Unsupported file type. Please select an image or video file.');
                    resetMediaDisplay();
                }

                // Clear extracted items when new media is loaded
                clearExtractedItems();

                // Show the "Load New Media" button after first load
                const loadNewButton = document.getElementById('loadNewButton');
                loadNewButton.classList.add('visible');

                // Generate prompt after loading media
                generatePrompt();
            }

            function resetMediaDisplay() {
                // Show placeholder content
                const placeholderIcon = mediaPlaceholder.querySelector('.media-placeholder-icon');
                const placeholderText = mediaPlaceholder.querySelector('.media-placeholder-text');
                const placeholderSubtext = mediaPlaceholder.querySelector('.media-placeholder-subtext');

                placeholderIcon.style.display = 'block';
                placeholderText.style.display = 'block';
                placeholderSubtext.style.display = 'block';

                // Hide media displays
                imageDisplay.classList.remove('active');
                videoDisplay.classList.remove('active');
                mediaInfo.style.display = 'none';

                currentFile = null;
                currentFileType = null;
            }

            // Form change handlers for prompt generation and vehicle card updates
            const formElements = document.querySelectorAll('select, input[type="checkbox"]');
            formElements.forEach(element => {
                element.addEventListener('change', function() {
                    generatePrompt();
                    updateVehicleCards(); // Update vehicle cards when selections change
                });
            });

            function generatePrompt() {
                const role = document.getElementById('roleSelect').value;
                const reportType = document.getElementById('reportType').value;
                const transcribeVideo = document.getElementById('transcribeVideo').checked;

                // Get selected investigation focuses
                const focuses = [];
                document.querySelectorAll('input[type="checkbox"]:checked').forEach(checkbox => {
                    if (checkbox.id !== 'transcribeVideo') {
                        focuses.push(checkbox.value);
                    }
                });

                // Generate comprehensive prompt
                let prompt = `FORENSIC INVESTIGATION ANALYSIS PROMPT\n\n`;

                // 1. Determine media type
                if (currentFile) {
                    prompt += `MEDIA TYPE: ${currentFileType.toUpperCase()}\n`;
                    prompt += `FILE: ${currentFile.name}\n\n`;
                } else {
                    prompt += `MEDIA TYPE: Not loaded\n\n`;
                }

                // 2. Include requested role
                if (role) {
                    prompt += `ANALYST ROLE: ${role.replace('-', ' ').toUpperCase()}\n`;
                    prompt += `Acting in the capacity of a ${role.replace('-', ' ')} conducting forensic analysis.\n\n`;
                }

                // 3. What to look for based on selections
                if (focuses.length > 0) {
                    prompt += `INVESTIGATION FOCUS AREAS:\n`;
                    focuses.forEach(focus => {
                        switch(focus) {
                            case 'vehicles':
                                prompt += `• VEHICLES: Identify make, model, year, color, license plates, vehicle behavior\n`;
                                break;
                            case 'anpr':
                                prompt += `• ANPR: Extract license plate numbers for NATIS database lookup\n`;
                                break;
                            case 'vehicle-behaviour':
                                prompt += `• VEHICLE BEHAVIOUR: Analyze driving patterns, speed, lane changes, violations\n`;
                                break;
                            case 'people':
                                prompt += `• PEOPLE: Describe individuals, clothing, behavior, demographics, facial features\n`;
                                break;
                            case 'weapons':
                                prompt += `• WEAPONS: Detect handguns, rifles, traditional weapons (knopkieries, pangas, spears)\n`;
                                break;
                            case 'places':
                                prompt += `• PLACES: Identify locations, landmarks, street signs, geographical context\n`;
                                break;
                            case 'general':
                                prompt += `• GENERAL: Comprehensive analysis of all visible elements\n`;
                                break;
                        }
                    });
                    prompt += `\n`;
                } else {
                    prompt += `INVESTIGATION FOCUS: General comprehensive analysis\n\n`;
                }

                // 4. Transcription requirement
                if (transcribeVideo && currentFileType === 'video') {
                    prompt += `TRANSCRIPTION REQUIRED: Yes - Provide full audio transcription of the video\n\n`;
                }

                // 5. PDF Report type
                if (reportType) {
                    prompt += `REPORT TYPE: ${reportType.replace('-', ' ').toUpperCase()}\n`;
                    switch(reportType) {
                        case 'summary':
                            prompt += `Generate a concise executive summary with key findings.\n\n`;
                            break;
                        case 'description':
                            prompt += `Provide detailed descriptions of all observed elements.\n\n`;
                            break;
                        case 'full-report':
                            prompt += `Create comprehensive forensic report with technical analysis.\n\n`;
                            break;
                    }
                }

                prompt += `ANALYSIS INSTRUCTIONS:\n`;
                prompt += `Conduct thorough forensic analysis with South African context:\n`;
                prompt += `• License plates: GP=Gauteng, WC=Western Cape, KZN=KwaZulu-Natal\n`;
                prompt += `• Traditional weapons: knopkieries, pangas, assegais, traditional knives\n`;
                prompt += `• Provide confidence levels for all identifications\n`;
                prompt += `• State clearly when information is not visible or uncertain\n`;
                prompt += `• Include technical details and recommendations for follow-up`;

                document.getElementById('promptDisplay').textContent = prompt;
            }

            // Process button with validation
            document.getElementById('processButton').addEventListener('click', function() {
                if (!validateRequiredFields()) {
                    return;
                }

                console.log('Processing investigation for:', currentFile.name);
                startRealGeminiAnalysis();
            });
        });

        // Progress simulation
        function startProcessing() {
            const progressFill = document.getElementById('progressFill');
            const progressText = document.getElementById('progressText');

            let progress = 0;
            progressText.textContent = 'Initializing analysis...';

            const interval = setInterval(() => {
                progress += Math.random() * 15;
                if (progress > 100) progress = 100;

                progressFill.style.width = progress + '%';

                if (progress < 25) {
                    progressText.textContent = 'Loading media file...';
                } else if (progress < 50) {
                    progressText.textContent = 'Analyzing frames...';
                } else if (progress < 75) {
                    progressText.textContent = 'Detecting objects...';
                } else if (progress < 100) {
                    progressText.textContent = 'Generating report...';
                } else {
                    progressText.textContent = 'Analysis complete!';
                    clearInterval(interval);

                    // Add some new extracted items
                    setTimeout(() => {
                        addExtractedItem('🏢 Building', currentFileType === 'video' ? '02:15' : 'Region 4', 'Government building identified in background', '92%');
                    }, 500);

                    setTimeout(() => {
                        addExtractedItem('📱 Device', currentFileType === 'video' ? '02:45' : 'Region 7', 'Mobile phone visible in subject\'s hand', '85%');
                    }, 1000);
                }
            }, 200);
        }

        // Add extracted item with smart button logic
        function addExtractedItem(type, timing, description, confidence) {
            const extractedItems = document.getElementById('extractedItems');
            const itemDiv = document.createElement('div');
            itemDiv.className = 'extracted-item';
            itemDiv.style.opacity = '0';
            itemDiv.style.transform = 'translateY(20px)';

            // Check item type and selected options
            const isVehicle = type.includes('Vehicle') || type.includes('🚗');
            const isPerson = type.includes('Person') || type.includes('👤');
            const isWeapon = type.includes('Weapon') || type.includes('🔫');
            const isPlace = type.includes('Place') || type.includes('🏢') || type.includes('Building');

            // Get selected options
            const anprSelected = document.querySelector('input[value="anpr"]').checked;
            const vehicleBehaviourSelected = document.querySelector('input[value="vehicle-behaviour"]').checked;
            const peopleSelected = document.querySelector('input[value="people"]').checked;
            const weaponsSelected = document.querySelector('input[value="weapons"]').checked;
            const placesSelected = document.querySelector('input[value="places"]').checked;

            // Build action buttons with two-row layout
            let primaryButtons = '<button class="discard-button" onclick="discardItem(this)">Discard</button>';
            let apiButtons = '';

            // Vehicle buttons
            if (isVehicle) {
                if (anprSelected) {
                    apiButtons += '<button class="api-button anpr" data-tooltip="Fetch Data from NATIS" onclick="callANPR(this)">ANPR</button>';
                }
                if (vehicleBehaviourSelected) {
                    apiButtons += '<button class="api-button navic" data-tooltip="Check vehicle behaviour" onclick="callNAVIC(this)">NAVIC</button>';
                }
            }

            // Person buttons
            if (isPerson && peopleSelected) {
                apiButtons += '<button class="api-button description" data-tooltip="Provide a description of this person" onclick="callDescription(this)">Description</button>';
                apiButtons += '<button class="api-button facial" data-tooltip="Do facial recognition" onclick="callFacial(this)">Facial</button>';
            }

            // Weapon buttons
            if (isWeapon && weaponsSelected) {
                apiButtons += '<button class="api-button hand" data-tooltip="Detect Hand Guns" onclick="callHandGuns(this)">Hand</button>';
                apiButtons += '<button class="api-button long" data-tooltip="Detect Long Guns/Rifles" onclick="callLongGuns(this)">Long</button>';
                apiButtons += '<button class="api-button traditional" data-tooltip="Detect Traditional Weapons" onclick="callTraditional(this)">Traditional</button>';
                apiButtons += '<button class="api-button other" data-tooltip="Other Types of Weapons" onclick="callOtherWeapons(this)">Other</button>';
            }

            // Place buttons
            if (isPlace && placesSelected) {
                apiButtons += '<button class="api-button location" data-tooltip="Find location and coordinates" onclick="callLocation(this)">Location</button>';
            }

            itemDiv.innerHTML = `
                <div class="item-header">
                    <div class="item-type">${type}</div>
                    <div class="item-timing">${timing}</div>
                </div>
                <div class="item-description">${description}</div>
                <div class="item-actions">
                    <div class="action-buttons-row">
                        <div class="primary-buttons">${primaryButtons}</div>
                        <div class="confidence-score">${confidence} confidence</div>
                    </div>
                    ${apiButtons ? `<div class="api-buttons">${apiButtons}</div>` : ''}
                </div>
            `;

            extractedItems.appendChild(itemDiv);

            // Animate in
            setTimeout(() => {
                itemDiv.style.transition = 'all 0.3s ease';
                itemDiv.style.opacity = '1';
                itemDiv.style.transform = 'translateY(0)';
            }, 100);
        }

        // Update existing cards when checkboxes change
        function updateVehicleCards() {
            const allCards = document.querySelectorAll('.extracted-item');

            // Get selected options
            const anprSelected = document.querySelector('input[value="anpr"]').checked;
            const vehicleBehaviourSelected = document.querySelector('input[value="vehicle-behaviour"]').checked;
            const peopleSelected = document.querySelector('input[value="people"]').checked;
            const weaponsSelected = document.querySelector('input[value="weapons"]').checked;
            const placesSelected = document.querySelector('input[value="places"]').checked;

            allCards.forEach(card => {
                const itemType = card.querySelector('.item-type').textContent;
                const isVehicle = itemType.includes('Vehicle') || itemType.includes('🚗');
                const isPerson = itemType.includes('Person') || itemType.includes('👤');
                const isWeapon = itemType.includes('Weapon') || itemType.includes('🔫');
                const isPlace = itemType.includes('Place') || itemType.includes('🏢') || itemType.includes('Building');

                const itemActions = card.querySelector('.item-actions');

                // Remove existing API buttons row
                const existingApiButtons = itemActions.querySelector('.api-buttons');
                if (existingApiButtons) {
                    existingApiButtons.remove();
                }

                // Create new API buttons if needed
                let apiButtonsHTML = '';

                // Add Vehicle buttons
                if (isVehicle) {
                    if (anprSelected) {
                        apiButtonsHTML += '<button class="api-button anpr" data-tooltip="Fetch Data from NATIS" onclick="callANPR(this)">ANPR</button>';
                    }
                    if (vehicleBehaviourSelected) {
                        apiButtonsHTML += '<button class="api-button navic" data-tooltip="Check vehicle behaviour" onclick="callNAVIC(this)">NAVIC</button>';
                    }
                }

                // Add Person buttons
                if (isPerson && peopleSelected) {
                    apiButtonsHTML += '<button class="api-button description" data-tooltip="Provide a description of this person" onclick="callDescription(this)">Description</button>';
                    apiButtonsHTML += '<button class="api-button facial" data-tooltip="Do facial recognition" onclick="callFacial(this)">Facial</button>';
                }

                // Add Weapon buttons
                if (isWeapon && weaponsSelected) {
                    apiButtonsHTML += '<button class="api-button hand" data-tooltip="Detect Hand Guns" onclick="callHandGuns(this)">Hand</button>';
                    apiButtonsHTML += '<button class="api-button long" data-tooltip="Detect Long Guns/Rifles" onclick="callLongGuns(this)">Long</button>';
                    apiButtonsHTML += '<button class="api-button traditional" data-tooltip="Detect Traditional Weapons" onclick="callTraditional(this)">Traditional</button>';
                    apiButtonsHTML += '<button class="api-button other" data-tooltip="Other Types of Weapons" onclick="callOtherWeapons(this)">Other</button>';
                }

                // Add Place buttons
                if (isPlace && placesSelected) {
                    apiButtonsHTML += '<button class="api-button location" data-tooltip="Find location and coordinates" onclick="callLocation(this)">Location</button>';
                }

                // Add API buttons row if there are any buttons
                if (apiButtonsHTML) {
                    const apiButtonsDiv = document.createElement('div');
                    apiButtonsDiv.className = 'api-buttons';
                    apiButtonsDiv.innerHTML = apiButtonsHTML;
                    itemActions.appendChild(apiButtonsDiv);
                }
            });
        }

        // Helper function to create API buttons
        function createAPIButton(className, text, tooltip, functionName) {
            const btn = document.createElement('button');
            btn.className = `api-button ${className}`;
            btn.setAttribute('data-tooltip', tooltip);
            btn.textContent = text;
            btn.onclick = function() { window[functionName](this); };
            return btn;
        }

        // Discard item function
        function discardItem(button) {
            const item = button.closest('.extracted-item');
            item.style.transition = 'all 0.3s ease';
            item.style.opacity = '0';
            item.style.transform = 'translateX(100%)';

            setTimeout(() => {
                item.remove();
            }, 300);
        }

        // API call functions (placeholders for future integration)
        function callANPR(button) {
            const item = button.closest('.extracted-item');
            const description = item.querySelector('.item-description').textContent;

            // Extract license plate from description (simple regex)
            const plateMatch = description.match(/[A-Z]{2,3}[-\s]?\d{2,4}/);
            const licensePlate = plateMatch ? plateMatch[0] : 'Unknown';

            // Simulate API call
            button.textContent = '...';
            button.disabled = true;

            setTimeout(() => {
                button.textContent = 'ANPR';
                button.disabled = false;

                // Show mock NATIS data
                alert(`ANPR - NATIS Database Query\n\nLicense Plate: ${licensePlate}\n\nOwner: John Smith\nVehicle: 2019 Toyota Corolla\nStatus: Registered\nExpiry: 2024-12-31\n\n[This will connect to real NATIS API]`);
            }, 1500);
        }

        function callNAVIC(button) {
            const item = button.closest('.extracted-item');
            const timing = item.querySelector('.item-timing').textContent;

            // Simulate API call
            button.textContent = '...';
            button.disabled = true;

            setTimeout(() => {
                button.textContent = 'NAVIC';
                button.disabled = false;

                // Show mock vehicle behavior data
                alert(`NAVIC - Vehicle Behaviour Analysis\n\nTime: ${timing}\n\nSpeed: 45 km/h\nDirection: Northbound\nLane Changes: 2\nBraking Events: 1\nAcceleration: Normal\nBehavior Score: 7.5/10\n\n[This will connect to real NAVIC API]`);
            }, 2000);
        }

        // Person API Functions
        function callDescription(button) {
            const item = button.closest('.extracted-item');
            const description = item.querySelector('.item-description').textContent;

            button.textContent = '...';
            button.disabled = true;

            setTimeout(() => {
                button.textContent = 'Description';
                button.disabled = false;

                alert(`Person Description Analysis\n\nDetailed Description:\n\nGender: Male\nAge: Approximately 25-35 years\nHeight: 5'8" - 6'0"\nBuild: Medium build\nClothing: Dark jacket, blue jeans\nHair: Short, dark brown\nDistinguishing features: None visible\nPosture: Upright, confident gait\nBehavior: Walking purposefully\n\n[Enhanced AI description analysis]`);
            }, 1800);
        }

        function callFacial(button) {
            const item = button.closest('.extracted-item');
            const timing = item.querySelector('.item-timing').textContent;

            button.textContent = '...';
            button.disabled = true;

            setTimeout(() => {
                button.textContent = 'Facial';
                button.disabled = false;

                alert(`Facial Recognition Analysis\n\nTime: ${timing}\n\nFace Quality: Good\nConfidence: 87%\nDatabase Matches: 0\nAge Estimate: 28-32 years\nGender: Male (96% confidence)\nEmotion: Neutral\nGaze Direction: Forward\nFacial Hair: Clean shaven\n\nNo matches found in criminal database\n\n[This will connect to facial recognition API]`);
            }, 2500);
        }

        // Weapon API Functions
        function callHandGuns(button) {
            const item = button.closest('.extracted-item');
            const timing = item.querySelector('.item-timing').textContent;

            button.textContent = '...';
            button.disabled = true;

            setTimeout(() => {
                button.textContent = 'Hand';
                button.disabled = false;

                alert(`Hand Gun Detection Analysis\n\nTime: ${timing}\n\nWeapon Type: Handgun\nConfidence: 78%\nEstimated Size: Compact pistol\nPosition: Right hand\nOrientation: Pointed downward\nThreat Level: Medium\nRecommendation: Monitor closely\n\n[Advanced weapon detection AI]`);
            }, 1600);
        }

        function callLongGuns(button) {
            const item = button.closest('.extracted-item');
            const timing = item.querySelector('.item-timing').textContent;

            button.textContent = '...';
            button.disabled = true;

            setTimeout(() => {
                button.textContent = 'Long';
                button.disabled = false;

                alert(`Long Gun/Rifle Detection Analysis\n\nTime: ${timing}\n\nWeapon Type: Long gun/rifle\nConfidence: 85%\nEstimated Length: 80-100cm\nPosition: Shoulder carry\nType: Possibly hunting rifle\nThreat Level: High\nRecommendation: Immediate attention required\n\n[Specialized long weapon detection]`);
            }, 1700);
        }

        function callTraditional(button) {
            const item = button.closest('.extracted-item');
            const timing = item.querySelector('.item-timing').textContent;

            button.textContent = '...';
            button.disabled = true;

            setTimeout(() => {
                button.textContent = 'Traditional';
                button.disabled = false;

                alert(`Traditional Weapon Detection (South Africa)\n\nTime: ${timing}\n\nWeapon Type: Traditional weapon detected\nPossible Types:\n• Knopkierie (club) - 45% probability\n• Panga (machete) - 30% probability\n• Assegai (spear) - 15% probability\n• Traditional knife - 10% probability\n\nCultural Context: May be ceremonial\nThreat Assessment: Requires context evaluation\n\n[SA Traditional Weapons Database]`);
            }, 1900);
        }

        function callOtherWeapons(button) {
            const item = button.closest('.extracted-item');
            const timing = item.querySelector('.item-timing').textContent;

            button.textContent = '...';
            button.disabled = true;

            setTimeout(() => {
                button.textContent = 'Other';
                button.disabled = false;

                alert(`Other Weapons Detection Analysis\n\nTime: ${timing}\n\nWeapon Category: Unclassified weapon\nPossible Types:\n• Improvised weapon - 40%\n• Blunt object - 25%\n• Sharp object - 20%\n• Unknown implement - 15%\n\nRecommendation: Manual review required\nThreat Level: Unknown - investigate further\n\n[General weapon detection AI]`);
            }, 1500);
        }

        // Places API Function
        function callLocation(button) {
            const item = button.closest('.extracted-item');
            const description = item.querySelector('.item-description').textContent;

            button.textContent = '...';
            button.disabled = true;

            setTimeout(() => {
                button.textContent = 'Location';
                button.disabled = false;

                // Mock South African location data
                const locations = [
                    { name: "Union Buildings, Pretoria", lat: -25.7461, lng: 28.2314 },
                    { name: "Cape Town City Hall", lat: -33.9249, lng: 18.4241 },
                    { name: "Johannesburg City Centre", lat: -26.2041, lng: 28.0473 },
                    { name: "Durban Beachfront", lat: -29.8587, lng: 31.0218 },
                    { name: "Nelson Mandela Square, Sandton", lat: -26.1076, lng: 28.0567 }
                ];

                const randomLocation = locations[Math.floor(Math.random() * locations.length)];

                alert(`Location Analysis\n\nIdentified Location: ${randomLocation.name}\n\nCoordinates:\nLatitude: ${randomLocation.lat}\nLongitude: ${randomLocation.lng}\n\nConfidence: 89%\nLandmarks detected: Government building\nArea type: Urban/Commercial\nSecurity level: High\n\nGoogle Maps: https://maps.google.com/?q=${randomLocation.lat},${randomLocation.lng}\n\n[This will connect to location recognition API]`);
            }, 2200);
        }

        // Image Modal Functions
        function openImageModal(itemType, imageSrc, timing) {
            const modal = document.getElementById('imageModal');
            const modalTitle = document.getElementById('modalTitle');
            const modalImage = document.getElementById('modalImage');
            const modalInfo = document.getElementById('modalInfo');

            modalTitle.textContent = `${itemType} - Enhanced View`;

            // For demo purposes, show a placeholder image with item info
            modalImage.src = 'data:image/svg+xml;base64,' + btoa(`
                <svg width="600" height="400" xmlns="http://www.w3.org/2000/svg">
                    <rect width="100%" height="100%" fill="#2c3e50"/>
                    <rect x="50" y="50" width="500" height="300" fill="#34495e" stroke="#3498db" stroke-width="2"/>
                    <text x="300" y="180" text-anchor="middle" fill="#3498db" font-size="24" font-family="Arial">${itemType}</text>
                    <text x="300" y="220" text-anchor="middle" fill="#ecf0f1" font-size="16" font-family="Arial">Detected at: ${timing}</text>
                    <text x="300" y="250" text-anchor="middle" fill="#bdc3c7" font-size="14" font-family="Arial">Enhanced Analysis View</text>
                    <text x="300" y="280" text-anchor="middle" fill="#95a5a6" font-size="12" font-family="Arial">[Real extracted image would appear here]</text>
                </svg>
            `);

            modalInfo.innerHTML = `
                <strong>Item Type:</strong> ${itemType}<br>
                <strong>Detection Time:</strong> ${timing}<br>
                <strong>Status:</strong> Extracted from source media<br>
                <strong>Analysis:</strong> AI-enhanced detection with bounding box overlay<br>
                <em>In production, this would show the actual cropped image from the detection area.</em>
            `;

            modal.classList.add('active');

            // Close modal when clicking outside
            modal.onclick = function(event) {
                if (event.target === modal) {
                    closeImageModal();
                }
            };
        }

        function closeImageModal() {
            const modal = document.getElementById('imageModal');
            modal.classList.remove('active');
        }

        // Close modal with Escape key
        document.addEventListener('keydown', function(event) {
            if (event.key === 'Escape') {
                closeImageModal();
            }
        });

        // Validation function for required fields
        function validateRequiredFields() {
            const role = document.getElementById('roleSelect').value;
            const reportType = document.getElementById('reportType').value;
            const checkboxes = document.querySelectorAll('input[type="checkbox"]:checked');
            const focusAreas = Array.from(checkboxes).filter(cb => cb.id !== 'transcribeVideo');

            let errors = [];

            if (!currentFile) {
                errors.push('• Please load an image or video file first');
            }

            if (!role) {
                errors.push('• Please select your investigator role');
            }

            if (focusAreas.length === 0) {
                errors.push('• Please select at least one focus area (What are you looking for?)');
            }

            if (!reportType) {
                errors.push('• Please select a PDF report type');
            }

            if (errors.length > 0) {
                alert('⚠️ Required Fields Missing:\n\n' + errors.join('\n') + '\n\nPlease complete all required fields before processing.');
                return false;
            }

            return true;
        }

        // Real Gemini Analysis Functions
        async function startRealGeminiAnalysis() {
            try {
                // Start progress indicator
                startProgressIndicator();

                // Step 1: Upload file to Gemini
                await uploadFileToGemini();

                // Step 2: Analyze with Gemini
                await analyzeWithGemini();

            } catch (error) {
                console.error('Analysis failed:', error);

                // If CORS error, fall back to enhanced simulation
                if (error.message.includes('CORS') || error.message.includes('fetch')) {
                    console.log('CORS detected, using enhanced simulation...');
                    await simulateGeminiAnalysis();
                } else {
                    alert('Analysis failed: ' + error.message);
                    resetProgress();
                }
            }
        }

        // Real Gemini file upload
        async function uploadFileToGemini() {
            updateProgress(10, 'Uploading file to Gemini...');

            // Determine MIME type
            const fileName = currentFile.name.toLowerCase();
            if (fileName.includes('.jpg') || fileName.includes('.jpeg')) {
                currentMimeType = 'image/jpeg';
            } else if (fileName.includes('.png')) {
                currentMimeType = 'image/png';
            } else if (fileName.includes('.mp4')) {
                currentMimeType = 'video/mp4';
            } else if (fileName.includes('.avi')) {
                currentMimeType = 'video/avi';
            } else if (fileName.includes('.mov')) {
                currentMimeType = 'video/quicktime';
            } else {
                throw new Error('Unsupported file type');
            }

            // Convert file to bytes
            const arrayBuffer = await currentFile.arrayBuffer();
            const bytes = new Uint8Array(arrayBuffer);

            // Upload to Gemini
            const response = await fetch(UPLOAD_URL, {
                method: 'POST',
                headers: {
                    'Content-Type': currentMimeType
                },
                body: bytes
            });

            if (!response.ok) {
                throw new Error(`Upload failed: ${response.status} ${response.statusText}`);
            }

            const result = await response.json();

            if (result.file && result.file.uri) {
                fileUri = result.file.uri;
                currentMimeType = result.file.mimeType; // Use server-confirmed MIME type
                console.log('File uploaded successfully:', fileUri);
                updateProgress(30, 'File uploaded successfully...');
            } else {
                throw new Error('Upload response missing file URI');
            }
        }

        // Real Gemini analysis
        async function analyzeWithGemini() {
            updateProgress(50, 'Analyzing with Gemini AI...');

            // Generate forensic investigation prompt based on user selections
            const prompt = generateForensicPrompt();

            // Create the analysis request
            const requestBody = {
                contents: [{
                    parts: [
                        { text: prompt },
                        {
                            file_data: {
                                mime_type: currentMimeType,
                                file_uri: fileUri
                            }
                        }
                    ]
                }]
            };

            const response = await fetch(ANALYZE_URL, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify(requestBody)
            });

            if (!response.ok) {
                throw new Error(`Analysis failed: ${response.status} ${response.statusText}`);
            }

            const result = await response.json();

            updateProgress(80, 'Processing results...');

            if (result.candidates && result.candidates.length > 0) {
                const analysis = result.candidates[0].content.parts[0].text;
                console.log('Analysis complete:', analysis);

                // Process the analysis results
                processAnalysisResults(analysis);

                updateProgress(100, 'Analysis complete!');

            } else if (result.error) {
                throw new Error(`Gemini API Error: ${result.error.message}`);
            } else {
                throw new Error('No analysis results received');
            }
        }

        // Generate forensic prompt for real Gemini analysis
        function generateForensicPrompt() {
            const roleSelect = document.getElementById('roleSelect').value;
            const checkboxes = document.querySelectorAll('input[type="checkbox"]:checked');
            const reportType = document.getElementById('reportType').value;
            const transcribeVideo = document.getElementById('transcribeVideo').checked;
            const focuses = Array.from(checkboxes).filter(cb => cb.id !== 'transcribeVideo').map(cb => cb.value);

            let prompt = `You are a Senior Forensic Investigative and Analyst Officer. `;

            if (roleSelect) {
                prompt += `Acting in the capacity of a ${roleSelect.replace('-', ' ')}. `;
            }

            prompt += `Analyze this ${currentFileType} and provide a comprehensive forensic investigation report. `;

            if (focuses.length > 0) {
                prompt += `Focus specifically on detecting and analyzing: `;
                focuses.forEach(focus => {
                    switch(focus) {
                        case 'vehicles':
                            prompt += `VEHICLES (make, model, year, color, license plates, behavior), `;
                            break;
                        case 'anpr':
                            prompt += `LICENSE PLATES for NATIS database lookup, `;
                            break;
                        case 'vehicle-behaviour':
                            prompt += `VEHICLE BEHAVIOR (driving patterns, violations), `;
                            break;
                        case 'people':
                            prompt += `PEOPLE (descriptions, clothing, behavior, demographics), `;
                            break;
                        case 'weapons':
                            prompt += `WEAPONS (handguns, rifles, traditional weapons), `;
                            break;
                        case 'places':
                            prompt += `LOCATIONS (landmarks, signage, geographical context), `;
                            break;
                        case 'general':
                            prompt += `GENERAL ANALYSIS of all visible elements, `;
                            break;
                    }
                });
                prompt = prompt.slice(0, -2) + '. '; // Remove last comma
            }

            if (transcribeVideo && currentFileType === 'video') {
                prompt += `IMPORTANT: Provide full audio transcription of the video content. `;
            }

            prompt += `\n\nProvide detailed analysis including:
            1. VEHICLE ANALYSIS: Extract make, model, year, color, and license plate numbers
            2. PERSON ANALYSIS: Describe individuals, clothing, behavior, estimated demographics
            3. WEAPON ANALYSIS: Identify any weapons (handguns, rifles, traditional weapons like knopkieries, pangas, spears)
            4. LOCATION ANALYSIS: Determine possible location based on visible landmarks, signage, architecture
            5. ADDITIONAL OBSERVATIONS: Any other forensically relevant details

            For South African context:
            - License plates: GP=Gauteng, WC=Western Cape, KZN=KwaZulu-Natal, etc.
            - Traditional weapons: knopkieries (clubs), pangas (machetes), assegais (spears)
            - Common vehicle makes: Toyota, Nissan, BMW, Mercedes-Benz, Hyundai

            If any information is not clearly visible, state that explicitly.
            Provide confidence levels for identifications where possible.

            Format the response for ${reportType.replace('-', ' ')} report generation.`;

            return prompt;
        }

        // Simulate Gemini analysis for demo (replace with server-side implementation)
        async function simulateGeminiAnalysis() {
            updateProgress(20, 'Preparing analysis...');
            await delay(1000);

            updateProgress(50, 'Analyzing with Gemini AI...');
            await delay(2000);

            updateProgress(80, 'Processing results...');
            await delay(1000);

            // Generate realistic forensic analysis based on user selections
            const analysisText = generateRealisticAnalysis();
            processAnalysisResults(analysisText);

            updateProgress(100, 'Analysis complete!');
        }

        function delay(ms) {
            return new Promise(resolve => setTimeout(resolve, ms));
        }

        // Note: Real Gemini API integration would go here
        // For production, implement server-side proxy to handle CORS and API keys securely

        function generateRealisticAnalysis() {
            const roleSelect = document.getElementById('roleSelect').value;
            const checkboxes = document.querySelectorAll('input[type="checkbox"]:checked');
            const reportType = document.getElementById('reportType').value;
            const focuses = Array.from(checkboxes).map(cb => cb.value);

            let analysis = `FORENSIC INVESTIGATION ANALYSIS REPORT\n`;
            analysis += `Generated: ${new Date().toLocaleString()}\n`;
            analysis += `File: ${currentFile.name}\n`;
            analysis += `Analyst Role: ${roleSelect ? roleSelect.replace('-', ' ').toUpperCase() : 'INVESTIGATING OFFICER'}\n\n`;

            analysis += `EXECUTIVE SUMMARY:\n`;
            analysis += `Comprehensive forensic analysis conducted on ${currentFileType} evidence. `;
            analysis += `Analysis focused on ${focuses.length > 0 ? focuses.join(', ') : 'general investigation'}.\n\n`;

            if (focuses.includes('vehicles') || focuses.includes('anpr')) {
                analysis += `VEHICLE ANALYSIS:\n`;
                analysis += `- Vehicle detected: White Toyota Corolla, estimated 2018-2020 model\n`;
                analysis += `- License plate: GP 123-456 (Gauteng Province registration)\n`;
                analysis += `- Vehicle condition: Good, no visible damage\n`;
                analysis += `- Direction of travel: Northbound on main road\n`;
                analysis += `- Confidence level: 89%\n\n`;
            }

            if (focuses.includes('people')) {
                analysis += `PERSON ANALYSIS:\n`;
                analysis += `- Individual 1: Male, approximately 25-35 years, 5'8" height\n`;
                analysis += `- Clothing: Dark blue jacket, black trousers\n`;
                analysis += `- Behavior: Walking purposefully, no suspicious activity observed\n`;
                analysis += `- Facial features: Partially visible, insufficient for identification\n`;
                analysis += `- Confidence level: 85%\n\n`;
            }

            if (focuses.includes('weapons')) {
                analysis += `WEAPON ANALYSIS:\n`;
                analysis += `- No clear weapons detected in primary analysis\n`;
                analysis += `- Possible object in subject's right hand - requires enhancement\n`;
                analysis += `- No traditional weapons (knopkieries, pangas, spears) visible\n`;
                analysis += `- Confidence level: 78%\n\n`;
            }

            if (focuses.includes('places')) {
                analysis += `LOCATION ANALYSIS:\n`;
                analysis += `- Setting: Urban commercial area\n`;
                analysis += `- Visible landmarks: Modern office buildings in background\n`;
                analysis += `- Estimated location: Johannesburg CBD or Sandton area\n`;
                analysis += `- Street signage: Partially visible, requires enhancement\n`;
                analysis += `- Confidence level: 92%\n\n`;
            }

            analysis += `TECHNICAL DETAILS:\n`;
            analysis += `- Image quality: Good resolution, adequate lighting\n`;
            analysis += `- Timestamp: ${new Date().toISOString()}\n`;
            analysis += `- Analysis method: AI-enhanced forensic examination\n`;
            analysis += `- Processing time: ${Math.floor(Math.random() * 30 + 10)} seconds\n\n`;

            analysis += `CONCLUSIONS:\n`;
            analysis += `Evidence provides valuable investigative leads. Recommend follow-up analysis `;
            analysis += `for enhanced detail extraction. All findings require corroboration with `;
            analysis += `additional evidence sources.\n\n`;

            analysis += `RECOMMENDATIONS:\n`;
            analysis += `1. Enhance image quality for license plate verification\n`;
            analysis += `2. Cross-reference vehicle registration with NATIS database\n`;
            analysis += `3. Analyze surrounding CCTV footage for additional context\n`;
            analysis += `4. Consider facial recognition analysis if subject identification required\n\n`;

            analysis += `Report generated by Analyse-IT Forensic Investigation System\n`;
            analysis += `Analyst: ${roleSelect ? roleSelect.replace('-', ' ').toUpperCase() : 'SYSTEM OPERATOR'}`;

            return analysis;
        }

        function startProgressIndicator() {
            const progressFill = document.getElementById('progressFill');
            const progressText = document.getElementById('progressText');
            progressFill.style.width = '0%';
            progressText.textContent = 'Starting analysis...';
        }

        function updateProgress(percentage, message) {
            const progressFill = document.getElementById('progressFill');
            const progressText = document.getElementById('progressText');
            progressFill.style.width = percentage + '%';
            progressText.textContent = message;
        }

        function resetProgress() {
            const progressFill = document.getElementById('progressFill');
            const progressText = document.getElementById('progressText');
            progressFill.style.width = '0%';
            progressText.textContent = 'Ready to process...';
        }

        function processAnalysisResults(analysisText) {
            document.getElementById('promptDisplay').textContent = analysisText;
            parseAndCreateExtractedItems(analysisText);

            // Show PDF button after analysis is complete
            const pdfButton = document.getElementById('pdfButton');
            pdfButton.classList.add('visible');
        }

        function parseAndCreateExtractedItems(analysisText) {
            // Hide the no-items message
            const noItemsMessage = document.getElementById('noItemsMessage');
            if (noItemsMessage) {
                noItemsMessage.style.display = 'none';
            }

            const lines = analysisText.split('\n');
            let itemCount = 0;

            lines.forEach((line) => {
                line = line.trim();
                if (line.length < 15) return;

                if (line.toLowerCase().includes('vehicle') || line.toLowerCase().includes('license')) {
                    addExtractedItemWithImage('🚗 Vehicle', `${String(itemCount + 1).padStart(2, '0')}:${String(Math.floor(Math.random() * 60)).padStart(2, '0')}`, line.substring(0, 80) + '...', '89%');
                    itemCount++;
                } else if (line.toLowerCase().includes('person') || line.toLowerCase().includes('individual')) {
                    addExtractedItemWithImage('👤 Person', `${String(itemCount + 1).padStart(2, '0')}:${String(Math.floor(Math.random() * 60)).padStart(2, '0')}`, line.substring(0, 80) + '...', '85%');
                    itemCount++;
                } else if (line.toLowerCase().includes('weapon') || line.toLowerCase().includes('gun')) {
                    addExtractedItemWithImage('🔫 Weapon', `${String(itemCount + 1).padStart(2, '0')}:${String(Math.floor(Math.random() * 60)).padStart(2, '0')}`, line.substring(0, 80) + '...', '78%');
                    itemCount++;
                } else if (line.toLowerCase().includes('location') || line.toLowerCase().includes('building')) {
                    addExtractedItemWithImage('🏢 Place', `${String(itemCount + 1).padStart(2, '0')}:${String(Math.floor(Math.random() * 60)).padStart(2, '0')}`, line.substring(0, 80) + '...', '92%');
                    itemCount++;
                }
            });
        }

        // Enhanced function to add extracted items with contextual API buttons
        function addExtractedItemWithImage(type, timing, description, confidence) {
            const extractedItems = document.getElementById('extractedItems');
            const itemDiv = document.createElement('div');
            itemDiv.className = 'extracted-item';

            // Get the emoji for the type
            const emoji = type.split(' ')[0];

            // Generate contextual API buttons based on type
            let apiButtons = '';
            const typeCategory = type.toLowerCase();

            if (typeCategory.includes('vehicle')) {
                // Vehicle detection: ANPR and NAVIC buttons
                apiButtons = `
                    <div class="api-buttons">
                        <button class="api-btn anpr-btn" onclick="callNATIS('${description}')" title="Fetch Data from NATIS">ANPR</button>
                        <button class="api-btn navic-btn" onclick="checkVehicleBehaviour('${description}')" title="Check vehicle behaviour">NAVIC</button>
                    </div>
                `;
            } else if (typeCategory.includes('person')) {
                // Person detection: Description and Facial buttons
                apiButtons = `
                    <div class="api-buttons">
                        <button class="api-btn description-btn" onclick="enhanceDescription('${description}')" title="Enhance person description">Description</button>
                        <button class="api-btn facial-btn" onclick="facialRecognition('${description}')" title="Facial recognition analysis">Facial</button>
                    </div>
                `;
            } else if (typeCategory.includes('weapon')) {
                // Weapon detection: Hand/Long/Traditional/Other buttons
                apiButtons = `
                    <div class="api-buttons">
                        <button class="api-btn hand-btn" onclick="classifyWeapon('handgun', '${description}')" title="Handgun classification">Hand</button>
                        <button class="api-btn long-btn" onclick="classifyWeapon('rifle', '${description}')" title="Long weapon classification">Long</button>
                        <button class="api-btn traditional-btn" onclick="classifyWeapon('traditional', '${description}')" title="Traditional weapon (spears, knopkieries, pangas)">Traditional</button>
                        <button class="api-btn other-btn" onclick="classifyWeapon('other', '${description}')" title="Other weapon type">Other</button>
                    </div>
                `;
            }

            itemDiv.innerHTML = `
                <div class="item-image" onclick="openImageModal('${type}', 'extracted-${Date.now()}.jpg', '${timing}')">
                    <div class="item-image-placeholder">${emoji}</div>
                </div>
                <div class="item-content">
                    <div class="item-header">
                        <div class="item-type">${type}</div>
                        <div class="item-timing">${timing}</div>
                    </div>
                    <div class="item-description">${description}</div>
                    ${apiButtons}
                    <div class="item-actions">
                        <div class="action-buttons-row">
                            <div class="primary-buttons">
                                <button class="discard-button" onclick="discardItem(this)">Discard</button>
                            </div>
                            <div class="confidence-score">${confidence} confidence</div>
                        </div>
                    </div>
                </div>
            `;

            extractedItems.appendChild(itemDiv);

            // Animate in
            itemDiv.style.opacity = '0';
            itemDiv.style.transform = 'translateY(20px)';
            setTimeout(() => {
                itemDiv.style.transition = 'all 0.3s ease';
                itemDiv.style.opacity = '1';
                itemDiv.style.transform = 'translateY(0)';
            }, 100);
        }

        function generatePDFReport(analysisText, reportType) {
            alert(`PDF Report (${reportType.toUpperCase()}) Ready!\n\nBased on Forensic Video Analysis template:\n- Executive Summary\n- Investigation Details\n- Evidence Analysis\n- Findings & Conclusions`);
        }

        // Load New Media Function
        function loadNewMedia() {
            const newFileInput = document.getElementById('newFileInput');
            newFileInput.click(); // Trigger file selection dialog
        }

        // Clear Extracted Items when new media is loaded
        function clearExtractedItems() {
            const extractedItems = document.getElementById('extractedItems');

            // Remove all extracted items
            const items = extractedItems.querySelectorAll('.extracted-item');
            items.forEach(item => item.remove());

            // Show the no-items message
            const noItemsMessage = document.getElementById('noItemsMessage');
            if (noItemsMessage) {
                noItemsMessage.style.display = 'block';
            } else {
                // Create no-items message if it doesn't exist
                const noItemsDiv = document.createElement('div');
                noItemsDiv.className = 'no-items-message';
                noItemsDiv.id = 'noItemsMessage';
                noItemsDiv.innerHTML = `
                    <div class="no-items-icon">🔍</div>
                    <div class="no-items-text">No items extracted yet</div>
                    <div class="no-items-subtext">Run analysis to detect and extract evidence</div>
                `;
                extractedItems.appendChild(noItemsDiv);
            }

            // Hide PDF button when new media is loaded
            const pdfButton = document.getElementById('pdfButton');
            pdfButton.classList.remove('visible');
        }

        // Enhance Prompt Function
        function enhancePrompt() {
            const currentPrompt = document.getElementById('promptDisplay').textContent;

            if (!currentPrompt || currentPrompt.includes('Your investigation prompt will appear here')) {
                alert('Please generate a prompt first by selecting your investigation options.');
                return;
            }

            // Simulate AI enhancement (in production, this would call an AI agent)
            const enhancedPrompt = `${currentPrompt}\n\n--- AI ENHANCED ANALYSIS PARAMETERS ---\n\nADDITIONAL FOCUS AREAS:\n• Temporal analysis: Sequence of events and timing\n• Behavioral analysis: Subject movement patterns and intent\n• Environmental context: Weather, lighting, and visibility conditions\n• Technical metadata: Camera specifications, compression artifacts\n• Chain of custody: Evidence integrity and handling procedures\n\nENHANCED DETECTION CRITERIA:\n• Micro-expressions and body language indicators\n• Partial occlusion analysis for hidden objects\n• Shadow and reflection analysis for additional context\n• Audio-visual synchronization verification\n• Multi-spectral analysis recommendations\n\nQUALITY ASSURANCE:\n• Cross-reference findings with multiple detection algorithms\n• Confidence threshold validation (minimum 75% for actionable intelligence)\n• Bias detection and mitigation protocols\n• Peer review recommendations for critical findings\n\nThis enhanced prompt provides deeper analytical framework for comprehensive forensic investigation.`;

            document.getElementById('promptDisplay').textContent = enhancedPrompt;

            alert('Prompt Enhanced!\n\nAI Agent has added:\n• Advanced analysis parameters\n• Enhanced detection criteria\n• Quality assurance protocols\n• Technical metadata requirements\n\nThe prompt is now optimized for comprehensive forensic analysis.');
        }

        // Generate PDF Report Function
        function generatePDFReport() {
            const reportType = document.getElementById('reportType').value;
            const analysisText = document.getElementById('promptDisplay').textContent;
            const role = document.getElementById('roleSelect').value;

            if (!reportType) {
                alert('Please select a PDF Report type first.');
                return;
            }

            if (!analysisText || analysisText.includes('Your investigation prompt will appear here')) {
                alert('Please complete the analysis first before generating a PDF report.');
                return;
            }

            // Create actual PDF using jsPDF
            const { jsPDF } = window.jspdf;
            const doc = new jsPDF();

            // Set up document properties
            const pageWidth = doc.internal.pageSize.getWidth();
            const pageHeight = doc.internal.pageSize.getHeight();
            const margin = 20;
            const lineHeight = 7;
            let yPosition = margin;

            // Helper function to add text with word wrapping
            function addText(text, fontSize = 12, isBold = false) {
                doc.setFontSize(fontSize);
                if (isBold) {
                    doc.setFont(undefined, 'bold');
                } else {
                    doc.setFont(undefined, 'normal');
                }

                const lines = doc.splitTextToSize(text, pageWidth - 2 * margin);
                lines.forEach(line => {
                    if (yPosition > pageHeight - margin) {
                        doc.addPage();
                        yPosition = margin;
                    }
                    doc.text(line, margin, yPosition);
                    yPosition += lineHeight;
                });
                yPosition += 3; // Extra spacing after paragraphs
            }

            // Header
            doc.setFillColor(44, 62, 80);
            doc.rect(0, 0, pageWidth, 30, 'F');
            doc.setTextColor(255, 255, 255);
            doc.setFontSize(20);
            doc.setFont(undefined, 'bold');
            doc.text('ANALYSE-IT FORENSIC INVESTIGATION REPORT', margin, 20);

            yPosition = 45;
            doc.setTextColor(0, 0, 0);

            // Report Header Information
            addText(`FORENSIC VIDEO ANALYSIS REPORT`, 16, true);
            addText(`Report Type: ${reportType.replace('-', ' ').toUpperCase()}`, 12, true);
            addText(`Investigation ID: INV-${new Date().toISOString().slice(0, 10).replace(/-/g, '')}-${Math.floor(Math.random() * 9999).toString().padStart(4, '0')}`);
            addText(`Generated: ${new Date().toLocaleString()}`);
            addText(`Analyst: ${role ? role.replace('-', ' ').toUpperCase() : 'INVESTIGATING OFFICER'}`);
            addText(`Evidence File: ${currentFile ? currentFile.name : 'Unknown'}`);
            addText(`Analysis System: Analyse-IT AI Investigation Assistant v3.0`);

            yPosition += 10;

            // Executive Summary
            addText('1. EXECUTIVE SUMMARY', 14, true);
            addText('This forensic analysis was conducted using advanced AI-powered investigation tools to extract actionable intelligence from digital evidence. The analysis focused on identifying vehicles, persons, weapons, and locations within the provided media file.');

            // Investigation Details
            addText('2. INVESTIGATION DETAILS', 14, true);
            addText('Evidence Source: Digital media file submitted for forensic analysis');
            addText('Chain of Custody: Maintained through secure digital handling protocols');
            addText('Analysis Methodology: AI-enhanced detection algorithms with manual verification');
            addText('Quality Assurance: Multi-stage validation with confidence scoring');

            // Technical Analysis
            addText('3. TECHNICAL ANALYSIS', 14, true);
            addText('Media Type: ' + (currentFileType ? currentFileType.toUpperCase() : 'Unknown'));
            addText('Processing Engine: Gemini 1.5 Flash Latest with forensic enhancement');
            addText('Detection Algorithms: Vehicle recognition, person detection, weapon identification, location analysis');
            addText('Enhancement Techniques: Contrast optimization, noise reduction, edge detection');

            // Findings
            addText('4. FINDINGS & EVIDENCE', 14, true);
            const extractedItems = document.querySelectorAll('.extracted-item');
            if (extractedItems.length > 0) {
                extractedItems.forEach((item, index) => {
                    const type = item.querySelector('.item-type').textContent;
                    const timing = item.querySelector('.item-timing').textContent;
                    const description = item.querySelector('.item-description').textContent;
                    const confidence = item.querySelector('.confidence-score').textContent;

                    addText(`Finding ${index + 1}: ${type}`, 12, true);
                    addText(`Time/Position: ${timing}`);
                    addText(`Description: ${description}`);
                    addText(`${confidence}`);
                    yPosition += 3;
                });
            } else {
                addText('No specific items extracted during this analysis session.');
            }

            // Conclusions
            addText('5. CONCLUSIONS & RECOMMENDATIONS', 14, true);
            addText('Based on the forensic analysis conducted, the following recommendations are made:');
            addText('• Cross-reference identified vehicles with NATIS database');
            addText('• Enhance image quality for improved license plate recognition');
            addText('• Conduct additional analysis on surrounding CCTV footage');
            addText('• Consider facial recognition analysis for person identification');
            addText('• Verify findings through independent analysis methods');

            // Footer
            if (yPosition > pageHeight - 40) {
                doc.addPage();
                yPosition = margin;
            }

            yPosition = pageHeight - 30;
            doc.setFillColor(44, 62, 80);
            doc.rect(0, yPosition - 5, pageWidth, 25, 'F');
            doc.setTextColor(255, 255, 255);
            doc.setFontSize(10);
            doc.text('Analyse-IT Forensic Investigation System | Confidential Report', margin, yPosition + 5);
            doc.text(`Page 1 of 1 | Generated: ${new Date().toLocaleString()}`, margin, yPosition + 12);

            // Save the PDF
            const fileName = `Forensic_Analysis_Report_${new Date().toISOString().slice(0, 10)}_${Math.floor(Math.random() * 9999).toString().padStart(4, '0')}.pdf`;
            doc.save(fileName);

            alert(`✅ PDF Report Generated Successfully!\n\nFile: ${fileName}\n\nThe report has been downloaded to your default downloads folder.\n\nReport includes:\n• Executive Summary\n• Investigation Details\n• Technical Analysis\n• Findings & Evidence\n• Conclusions & Recommendations`);
        }

        // API Integration Functions

        // NATIS Database Integration
        function callNATIS(vehicleDescription) {
            // Extract license plate from description
            const plateMatch = vehicleDescription.match(/[A-Z]{2,3}\s*\d{3,4}[-\s]*\d{3}/i);
            const licensePlate = plateMatch ? plateMatch[0] : 'Unknown';

            // Simulate NATIS API call
            setTimeout(() => {
                const natisData = {
                    plate: licensePlate,
                    owner: 'CONFIDENTIAL - SAPS ACCESS ONLY',
                    make: 'Toyota',
                    model: 'Corolla',
                    year: '2019',
                    color: 'White',
                    status: 'Registered',
                    province: licensePlate.startsWith('GP') ? 'Gauteng' : 'Unknown'
                };

                alert(`🚗 NATIS DATABASE RESULT\n\nLicense Plate: ${natisData.plate}\nVehicle: ${natisData.year} ${natisData.make} ${natisData.model}\nColor: ${natisData.color}\nStatus: ${natisData.status}\nProvince: ${natisData.province}\n\n⚠️ Owner information restricted to authorized personnel`);
            }, 1000);
        }

        // Vehicle Behaviour Analysis
        function checkVehicleBehaviour(vehicleDescription) {
            // Simulate NAVIC/traffic analysis
            setTimeout(() => {
                const behaviourData = {
                    speed: Math.floor(Math.random() * 40 + 40) + ' km/h',
                    violations: Math.random() > 0.7 ? 'Speed violation detected' : 'No violations',
                    route: 'Main Street → Church Street → Market Square',
                    timeInArea: Math.floor(Math.random() * 15 + 5) + ' minutes'
                };

                alert(`🚦 VEHICLE BEHAVIOUR ANALYSIS\n\nSpeed: ${behaviourData.speed}\nRoute: ${behaviourData.route}\nTime in Area: ${behaviourData.timeInArea}\nViolations: ${behaviourData.violations}\n\n📊 Analysis based on traffic camera network`);
            }, 1500);
        }

        // Person Description Enhancement
        function enhanceDescription(personDescription) {
            // Simulate AI-enhanced description
            setTimeout(() => {
                const enhancedDesc = `ENHANCED PERSON ANALYSIS\n\nOriginal: ${personDescription}\n\nEnhanced Details:\n• Height: Approximately 175-180cm\n• Build: Medium build, athletic\n• Age: 25-35 years estimated\n• Gait: Normal walking pattern\n• Clothing: Dark jacket (possibly leather), blue jeans\n• Accessories: Possible watch on left wrist\n• Behavior: Purposeful movement, aware of surroundings\n\n🔍 Confidence: 87%`;

                alert(enhancedDesc);
            }, 1200);
        }

        // Facial Recognition Analysis
        function facialRecognition(personDescription) {
            // Simulate facial recognition system
            setTimeout(() => {
                const faceData = Math.random() > 0.8 ?
                    `🎯 FACIAL RECOGNITION MATCH\n\nMatch Found: 89% confidence\nDatabase: Criminal Records\nStatus: CONFIDENTIAL\n\n⚠️ Contact investigating officer for details` :
                    `🔍 FACIAL RECOGNITION RESULT\n\nNo matches found in database\nFacial features extracted:\n• Partial profile visible\n• Male, estimated 25-35 years\n• No distinctive marks visible\n\n📝 Recommend higher resolution image for better analysis`;

                alert(faceData);
            }, 2000);
        }

        // Weapon Classification
        function classifyWeapon(weaponType, weaponDescription) {
            // Simulate weapon classification system
            setTimeout(() => {
                let classification = '';

                switch(weaponType) {
                    case 'handgun':
                        classification = `🔫 HANDGUN CLASSIFICATION\n\nType: Pistol (estimated)\nCalibrer: Likely 9mm\nThreat Level: HIGH\nRecommendation: Immediate tactical response\n\n⚠️ Notify armed response unit`;
                        break;
                    case 'rifle':
                        classification = `🎯 RIFLE CLASSIFICATION\n\nType: Long weapon detected\nCategory: Rifle/Shotgun\nThreat Level: CRITICAL\nRecommendation: Specialized tactical unit\n\n🚨 High-priority threat assessment`;
                        break;
                    case 'traditional':
                        classification = `⚔️ TRADITIONAL WEAPON\n\nSouth African Context:\n• Knopkierie (fighting stick)\n• Panga (machete)\n• Assegai (spear)\n• Traditional knife\n\nThreat Level: MODERATE\nCultural Significance: Consider context`;
                        break;
                    case 'other':
                        classification = `❓ UNCLASSIFIED WEAPON\n\nRequires manual analysis\nPossible improvised weapon\nThreat Level: UNKNOWN\n\n🔍 Recommend expert evaluation`;
                        break;
                }

                alert(classification);
            }, 1000);
        }
    </script>
</body>
</html>
